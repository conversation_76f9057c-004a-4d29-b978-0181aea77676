ensure_e2e_tenant() {
  local db_name=$1
  psql -c "DROP DATABASE IF EXISTS $db_name;"
  psql -c "CREATE DATABASE $db_name;"
  sqitch --chdir "./database/src/tenant/Deploy" deploy --verify --target "db:pg://$PGUSER:$PGPASSWORD@postgres/$db_name"
  sqitch --chdir "./database/src/tenant/Rebase" deploy --verify --target "db:pg://$PGUSER:$PGPASSWORD@postgres/$db_name"
}

ensure_e2e_tenant "cm_tenant_1"
ensure_e2e_tenant "cm_tenant_2"
