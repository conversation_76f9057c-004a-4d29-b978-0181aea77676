BEGIN;

CREATE TABLE IF NOT EXISTS settings
(
    id       uuid              NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_settings primary key,
    name        VARCHAR(150)   NOT NULL,
    description VARCHAR(250)   NOT NULL DEFAULT '',
    type        VARCHAR(150)   NOT NULL,
    site_id     uuid,
    data        jsonb          DEFAULT '{}'  NOT NULL,
    active      boolean        NOT NULL,
    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS idx_settings_name ON settings (lower(name));
CREATE INDEX IF NOT EXISTS idx_settings_type ON settings (type);
CREATE INDEX IF NOT EXISTS idx_settings_data ON settings USING gin (data);
CREATE INDEX IF NOT EXISTS idx_settings_search ON settings USING gin ((settings.id || ' ' || settings.type || ' ' || settings.name || ' ' || coalesce(settings.description, ' ')) gin_trgm_ops);

GRANT ALL ON TABLE settings to contentmanager_application_user;

COMMIT;
