-- Deploy cm_tenant_db_DeploySchema:NavigationTable to pg

BEGIN;

    CREATE TABLE navigation
    (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
		content_id uuid NOT NULL,
		site_id uuid,
		path ltree,
        visible boolean DEFAULT TRUE,
		active boolean DEFAULT TRUE,
		CONSTRAINT pk_navigation_id PRIMARY KEY(id),
        CONSTRAINT fk_content_id FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
    )WITH (
		OIDS=FALSE
	);

    CREATE INDEX ix_nav_path_gist ON navigation USING GIST (path);

    GRANT ALL ON TABLE navigation to contentmanager_application_user;

COMMIT;
