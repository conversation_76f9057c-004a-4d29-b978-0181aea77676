BEGIN;

ALTER TABLE account
    ADD COLUMN IF NOT EXISTS external_groups text[] NOT NULL DEFAULT ARRAY[]::text[],
    ADD COLUMN IF NOT EXISTS manual_groups uuid[] NOT NULL DEFAULT ARRAY[]::uuid[],
    ADD COLUMN IF NOT EXISTS native_groups uuid[] NOT NULL DEFAULT ARRAY[]::uuid[],
    ADD COLUMN IF NOT EXISTS created_at    TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ADD COLUMN IF NOT EXISTS created_by    uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085',
    ADD COLUMN IF NOT EXISTS updated_at    TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ADD COLUMN IF NOT EXISTS updated_by    uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085'
;
CREATE INDEX IF NOT EXISTS idx_gin_account_external_groups ON account USING gin(external_groups);
CREATE INDEX IF NOT EXISTS idx_gin_account_manual_groups   ON account USING gin(manual_groups);
CREATE INDEX IF NOT EXISTS idx_gin_account_native_groups   ON account USING gin(native_groups);

CREATE EXTENSION if not exists pg_trgm;
CREATE OR REPLACE FUNCTION immutable_concat_account_cols(p_account public.account)
    RETURNS text AS $$
BEGIN
    RETURN p_account.id || ' '
               || p_account.firstname || ' '
               || p_account.lastname || ' '
               || p_account.email || ' '
               || array_to_string(p_account.external_groups, ' ') || ' '
               || array_to_string(p_account.manual_groups::text[], ' ') || ' '
        || array_to_string(p_account.native_groups::text[], ' ');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE INDEX IF NOT EXISTS idx_composite_account_trgm ON public.account USING gin(immutable_concat_account_cols(account) gin_trgm_ops);

CREATE TYPE group_type as ENUM (
    'external'
    , 'manual'
    );

ALTER TABLE security_group
    ADD COLUMN IF NOT EXISTS type group_type  NOT NULL DEFAULT 'external',
    ADD COLUMN IF NOT EXISTS depends_on       text[] NOT NULL DEFAULT ARRAY[]::text[],
    ADD COLUMN IF NOT EXISTS audience         text[] NOT NULL DEFAULT ARRAY[]::text[],
    ADD COLUMN IF NOT EXISTS available_groups uuid[] NOT NULL DEFAULT ARRAY[]::uuid[],
    ADD COLUMN IF NOT EXISTS created_at       TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ADD COLUMN IF NOT EXISTS created_by       uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085',
    ADD COLUMN IF NOT EXISTS updated_at       TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ADD COLUMN IF NOT EXISTS updated_by       uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085'
;
CREATE OR REPLACE FUNCTION immutable_concat_security_group_cols(p_sg public.security_group)
    RETURNS text AS $$
BEGIN
    RETURN p_sg.id || ' '
               || p_sg.name || ' '
               || p_sg.description || ' '
               || p_sg.role_id || ' '
               || p_sg.site_id || ' '
               || array_to_string(p_sg.external_id_list, ' ') || ' '
               || array_to_string(p_sg.audience, ' ') || ' '
               || array_to_string(p_sg.available_groups::text[], ' ') || ' '
    ;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE INDEX IF NOT EXISTS idx_composite_security_group_trgm ON public.security_group USING gin(immutable_concat_security_group_cols(security_group) gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_gin_security_group_depends_on       ON security_group USING gin(depends_on);
CREATE INDEX IF NOT EXISTS idx_gin_security_group_audience  ON security_group USING gin(audience);
CREATE INDEX IF NOT EXISTS idx_gin_security_group_available_groups  ON security_group USING gin(available_groups);
CREATE INDEX IF NOT EXISTS idx_gin_security_group_external_id_list ON security_group USING gin(external_id_list);

COMMIT;
