import { Box, Checkbox, FormControlLabel, FormGroup, FormHelperText, FormLabel } from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers'
import React from 'react'
import moment, { Moment } from 'moment-timezone'
import { EventTimePicker } from './EventDateTimePicker'
import { RRuleSelector } from '@/common/editor/RRuleSelector'
import { useTimezone } from '@/common/editor/useTimezone'
import { EventSchedule } from '@/common/editor/rruleUtils'

interface EventDateTimePickerV2Props {
    disabled?: boolean
    value: EventSchedule
    onChange: (e: EventSchedule) => void
    timeStep?: number
    error?: string
}

export function EventDateTimePickerV2({ disabled, value, onChange, timeStep = 15, error }: EventDateTimePickerV2Props) {
    const timezone = useTimezone()

    if (!timezone) {
        return null
    }

    const fromString = (str: string) => withAppropriateOffset(str, value.isAllDay, timezone)
    const minusOneDay = (date: Moment) => date.clone().subtract({ days: 1 })

    return (
        <Box display='flex' flexDirection='column' gap='12px'>
            <Box display='flex' gap='12px'>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                disabled={disabled}
                                checked={value.isAllDay || false}
                                onChange={(e) =>
                                    onChange(
                                        serializeEventSchedule({
                                            ...value,
                                            startdate: null,
                                            enddate: null,
                                            isAllDay: e.target.checked,
                                            rrule: '',
                                            exdate: [],
                                            rdate: []
                                        })
                                    )
                                }
                                name='first'
                            />
                        }
                        label='All Day'
                    />
                </FormGroup>

                {!value.isAllDay && (
                    <FormLabel sx={{ padding: 1.5, marginLeft: 'auto' }}>Timezone: {timezone}</FormLabel>
                )}
            </Box>
            <FormLabel error={!!error}>Start Date:</FormLabel>
            <Box display='flex' gap='12px'>
                <DatePicker
                    disabled={disabled}
                    value={(value.startdate && fromString(value.startdate)) || null}
                    slotProps={{
                        textField: { size: 'small', variant: 'standard' }
                    }}
                    onChange={(val: moment.Moment | null) => {
                        onChange(
                            serializeEventSchedule({
                                ...value,
                                rrule: '',
                                startdate: val?.isValid()
                                    ? (value.startdate && fromString(value.startdate)
                                          ? setMomentDateDay(fromString(value.startdate), val)
                                          : val
                                      )?.toISOString()
                                    : null
                            })
                        )
                    }}
                />
                {!value.isAllDay && !disabled && (
                    <EventTimePicker
                        id={'event-startDate'}
                        timeStep={timeStep}
                        startDate={(value.startdate && fromString(value.startdate)) || null}
                        timeFieldValue={(value.startdate && fromString(value.startdate)) || null}
                        onChange={(val) => {
                            onChange(
                                serializeEventSchedule({
                                    ...value,
                                    rrule: '',
                                    startdate:
                                        (value.startdate && fromString(value.startdate)
                                            ? setMomentDateTime(fromString(value.startdate), val)
                                            : val
                                        )?.toISOString() || null
                                })
                            )
                        }}
                    />
                )}
            </Box>
            <FormLabel error={!!error}>End Date:</FormLabel>
            <Box display='flex' gap='12px'>
                <DatePicker
                    disabled={disabled}
                    value={
                        (value.enddate &&
                            (value.isAllDay ? minusOneDay(fromString(value.enddate)) : fromString(value.enddate))) ||
                        null
                    }
                    slotProps={{
                        textField: { size: 'small', variant: 'standard' }
                    }}
                    onChange={(val: moment.Moment | null) => {
                        onChange(
                            serializeEventSchedule({
                                ...value,
                                enddate: val?.isValid()
                                    ? (value.enddate && fromString(value.enddate)
                                          ? setMomentDateDay(fromString(value.enddate), val)
                                          : val
                                      )?.toISOString()
                                    : null
                            })
                        )
                    }}
                />
                {!value.isAllDay && !disabled && (
                    <EventTimePicker
                        id={'event-endDate'}
                        timeStep={timeStep}
                        startDate={(value.startdate && fromString(value.startdate)) || undefined}
                        endDate={(value.enddate && fromString(value.enddate)) || undefined}
                        timeFieldValue={(value.enddate && fromString(value.enddate)) || null}
                        disablePast={
                            !!(
                                value.enddate &&
                                value.startdate &&
                                fromString(value.startdate)?.isSame(fromString(value.enddate), 'day')
                            )
                        }
                        showHelperText
                        onChange={(e) => {
                            if (value.enddate && fromString(value.enddate)) {
                                const updated = setMomentDateTime(fromString(value.enddate), e)
                                onChange(
                                    serializeEventSchedule({
                                        ...value,
                                        enddate: (updated ? updated : e)?.toISOString() || null
                                    })
                                )
                            } else {
                                if (
                                    value.enddate &&
                                    value.startdate &&
                                    fromString(value.startdate) &&
                                    setMomentDateTime(fromString(value.enddate), e).isBefore(
                                        value.startdate && fromString(value.startdate)
                                    )
                                ) {
                                    onChange(
                                        serializeEventSchedule({
                                            ...value,
                                            enddate:
                                                value.startdate &&
                                                fromString(value.startdate).add({ day: 1 }).toISOString()
                                        })
                                    )
                                } else if (value.startdate && fromString(value.startdate)) {
                                    onChange(
                                        serializeEventSchedule({
                                            ...value,
                                            enddate: setMomentDateTime(fromString(value.startdate), e).toISOString()
                                        })
                                    )
                                } else {
                                    onChange(
                                        serializeEventSchedule({
                                            ...value,
                                            enddate: null
                                        })
                                    )
                                }
                            }
                        }}
                    />
                )}
            </Box>
            {!!error && <FormHelperText error>{error}</FormHelperText>}

            <RRuleSelector
                disabled={!value.enddate || !value.startdate || disabled || !!error}
                value={value}
                onChange={(e) => {
                    if (!value.enddate) return

                    onChange(
                        serializeEventSchedule({
                            ...value,
                            enddate: value.isAllDay
                                ? minusOneDay(fromString(value.enddate)).toISOString()
                                : fromString(value.enddate).toISOString(),
                            rrule: e.rrule,
                            exdate: e.exdate,
                            rdate: e.rdate
                        })
                    )
                }}
            />
        </Box>
    )
}

export function setMomentDateTime(date: Moment, dateWithNewTime: Date | Moment | string) {
    const momentDateWithNewTime = moment.utc(dateWithNewTime)
    const res = date.utc().clone().set({
        hour: momentDateWithNewTime.hour(),
        minute: momentDateWithNewTime.minute()
    })
    return res
}

export function setMomentDateDay(date: Moment, dateWithNewDay: Date | Moment | string) {
    const momentDateWithNewDay = moment(dateWithNewDay)
    return date.clone().set({
        date: momentDateWithNewDay.date(),
        month: momentDateWithNewDay.month(),
        year: momentDateWithNewDay.year()
    })
}

function withAppropriateOffset(date, isAllDay, timezone) {
    return moment?.(date).tz(isAllDay ? 'UTC' : timezone)
}

function convertToUTCAtZeroHours(date) {
    if (!(date instanceof Date)) {
        date = new Date(date)
    }
    const fullYear = date.getUTCFullYear()
    const month = date.getUTCMonth()
    const utcDate = date.getUTCDate()
    const asUTC = Date.UTC(fullYear, month, utcDate)
    return new Date(asUTC)
}

function serializeEventSchedule({
    isAllDay = false,
    startdate,
    enddate,
    rrule = '',
    rdate = [],
    exdate = []
}: {
    isAllDay?: boolean | undefined
    startdate: any
    enddate: any
    rrule?: string | undefined
    rdate?: string[] | undefined
    exdate?: string[] | undefined
}) {
    startdate = startdate || (isAllDay ? moment().startOf('day').toISOString() : moment().startOf('hour').toISOString())
    enddate =
        enddate ||
        (isAllDay ? moment(startdate).add({ day: 1 }).toISOString() : moment(startdate).add({ hour: 1 }).toISOString())

    if (isAllDay) {
        startdate = convertToUTCAtZeroHours(startdate)
        const jsEndDate = convertToUTCAtZeroHours(enddate) || convertToUTCAtZeroHours(startdate)
        enddate = new Date(jsEndDate.setDate(jsEndDate.getDate() + 1))
    }
    return {
        isAllDay,
        startdate: moment(startdate).toISOString(),
        enddate: moment(enddate).toISOString(),
        rrule,
        rdate,
        exdate
    }
}
