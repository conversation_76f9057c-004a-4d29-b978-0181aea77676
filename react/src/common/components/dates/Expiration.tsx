// import React, { useEffect, useRef, useState } from 'react'
// import { is } from '../../helpers'
// import { Button, Checkbox, FormControl, FormControlLabel, FormGroup, FormLabel, TextField } from '@mui/material'
// //@ts-ignore
// import DatePicker from 'react-datepicker'
//
// type DateValue = Date | null | undefined
//
// export function mustDate(dateLike: Date | string | null | undefined) {
//     if (dateLike instanceof Date) {
//         return dateLike
//     } else if (typeof dateLike === 'string') {
//         return new Date(dateLike)
//     } else if (!dateLike) {
//         return null
//     }
// }
//
// export default function Expiration({
//     disabled,
//     errors,
//     value,
//     onChange
// }: {
//     disabled: boolean
//     errors: Record<string, boolean>
//     value: DateValue
//     onChange: (v: DateValue) => void
// }) {
//     const [isComponentActivated, setIsComponentActivated] = useState<boolean>(false)
//     const [previous, setPrevious] = useState<DateValue>(null)
//     const expirationRef = useRef(DatePicker)
//
//     // If the inbound value is valid, we want to capture a copy of it to be used
//     // in handleCheckbox - when a user switches between "isComponentActivated" true / false
//     // we can inject the previous value.
//     useEffect(() => {
//         if (value) {
//             setPrevious(value)
//         }
//     }, [value])
//
//     // On dis-mount we call onChange with null, as dismount on this useEffect (with empty dependencies)
//     // indicates a user has switched from published to a draft or vice-versa.
//     useEffect(() => {
//         if (value) {
//             setIsComponentActivated(true)
//         }
//         return () => {
//             onChange(null)
//         }
//     }, [])
//
//     const closeExpirationClick = () => {
//         return expirationRef?.current?.setOpen?.(false)
//     }
//     const CustomDateTimeInput = ({ value, onClick, label, errorKey, disabled, customInputProps, style }) => {
//         const st = { width: '98%', ...(is.object(style) ? style : {}) }
//         console.log({
//             errors,
//             errorKey,
//             atKey: errors?.[errorKey]
//         })
//         return (
//             <TextField
//                 variant='standard'
//                 error={Boolean(errors?.[errorKey])}
//                 onClick={onClick}
//                 value={value}
//                 placeholder={label}
//                 label={label}
//                 disabled={disabled}
//                 inputProps={customInputProps ? customInputProps : {}}
//                 style={st}
//                 helperText={
//                     Boolean(errors?.[errorKey]) ? 'Please set Expiration Date or uncheck Set Unpublish Date' : ''
//                 }
//             >
//                 {value}
//             </TextField>
//         )
//     }
//     const handleCheckbox = () => {
//         const next = !isComponentActivated
//         if (!next) {
//             // Return null
//             // :user turned off expiry, but they previously had a value:
//             handleChange(null)
//         } else if (next && previous) {
//             //  set back to previous value?
//             // :user is returning, they previously registered a value:
//             handleChange(previous)
//         } else if (next && !previous) {
//             // opening and nothing, should be empty
//             // call onChange with undefined to indicate that the component is active
//             // and is waiting for a proper Date value.
//             handleChange(undefined)
//         }
//         setIsComponentActivated(!isComponentActivated)
//     }
//     const handleChange = (evt: any) => {
//         if (value && value !== previous) {
//             setPrevious(mustDate(value))
//         }
//         if (is.func(onChange)) {
//             onChange(evt)
//         }
//     }
//     return (
//         <div>
//             <FormControl variant='standard' component='fieldset' style={{ marginTop: '1vh' }} disabled={disabled}>
//                 <FormLabel component='legend'>Expire Content</FormLabel>
//                 <FormGroup>
//                     <FormControlLabel
//                         control={
//                             <Checkbox
//                                 disabled={disabled}
//                                 onChange={handleCheckbox}
//                                 checked={isComponentActivated}
//                                 name='first'
//                             />
//                         }
//                         label='Set Unpublish Date'
//                     />
//                 </FormGroup>
//             </FormControl>
//             {isComponentActivated && (
//                 <DatePicker
//                     ref={expirationRef}
//                     selected={value}
//                     onChange={handleChange}
//                     customInput={
//                         //@ts-ignore
//                         <CustomDateTimeInput
//                             disabled={disabled}
//                             errorKey='expirationDate'
//                             customInputProps={{ style: { paddingTop: 0 } }}
//                         />
//                     }
//                     startDate={value}
//                     disabled={disabled}
//                     timeFormat='p'
//                     timeIntervals={15}
//                     dateFormat='MM/dd/yyyy h:mm aa'
//                     withPortal
//                     selectsStart
//                     showTimeInput
//                     timeInputLabel='Time:'
//                     shouldCloseOnSelect={false}
//                 >
//                     <div>
//                         <Button
//                             size='small'
//                             color='primary'
//                             variant='outlined'
//                             onClick={closeExpirationClick}
//                             style={{ position: 'absolute', top: '90%', right: '5%' }}
//                         >
//                             Close
//                         </Button>
//                     </div>
//                 </DatePicker>
//             )}
//         </div>
//     )
// }
