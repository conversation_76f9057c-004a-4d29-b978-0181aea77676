import React, { useState } from 'react'
import { CustomMenuItem } from './CustomMenu'
import { CircularProgress } from '@mui/material'
import { RestoreFromTrashOutlined } from '@mui/icons-material'
import { BaseContextMenuItemProps } from './types'
import { failure, success } from '../../../helpers/result'
import { restoreContentQuery } from '@/pkgs/content/queries'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

export const ContentRestoreMenuItem = ({
    id,
    disabled,
    onChange
}: BaseContextMenuItemProps & {
    id: string
}) => {
    const [loading, setLoading] = useState<boolean>(false)

    const handleRestore = async () => {
        try {
            setLoading(true)
            await restoreContentQuery(id)
            onChange?.(success(null))
        } catch (err) {
            console.error(failure(err))
            notify(guessErrorMessage(err), 'error')
        } finally {
            setLoading(false)
        }
    }

    return (
        <CustomMenuItem text={'Restore'} disabled={Boolean(disabled)} onClick={handleRestore}>
            {loading ? <CircularProgress style={{ height: '24px', width: '24px' }} /> : <RestoreFromTrashOutlined />}
        </CustomMenuItem>
    )
}
