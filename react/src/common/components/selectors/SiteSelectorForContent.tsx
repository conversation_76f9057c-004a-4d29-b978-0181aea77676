import React, { useEffect, useMemo, useState } from 'react'
import { z } from 'zod'
import {
    Box,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    ListItemText,
    MenuItem,
    OutlinedInput,
    Select,
    Switch
} from '@mui/material'
import { useTagsQueryV2 } from '../../../pkgs/system/tags/queries'
import { atom, useAtom } from 'jotai'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Dialog from '@mui/material/Dialog'
import { isRouteExternal } from '../../../helpers'
import { CustomMenuItem } from '../custom-context-menu/CustomMenu'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { useAppContext, useCurrentSite, useSitesForContent } from '../../../pkgs/auth/atoms'
import { entityScope, typeToEntityScope } from '../../../pkgs/auth/entityScope'
import { asSecured } from '../../../pkgs/auth/permissions/securityMapping'
import { Site } from '../../../pkgs/auth/types'
import { BASE } from '../../constants'
import { TagType } from '../../../pkgs/system/tags/types'
import { TagsSelectorForSites } from '@/pkgs/system/tags/TagsSelectorForSites'

const site = z.object({
    ID: z.string(),
    Name: z.string(),
    PrimaryDomain: z.string(),
    Tags: z.array(z.string()),
    RestrictedByParent: z.boolean()
})
const sitesForSelector = z.array(site)
export type SitesForSelector = z.infer<typeof sitesForSelector>
export type SiteForSelector = z.infer<typeof site>

export type SiteSelectorForContentProps = {
    Selected: string[] | undefined
    ParentSitesIDs?: string[] // parent site IDs
    ContentType: entityScope
    IgnoreSiteID?: boolean
    OnChange?: (selected: string[], departmentID: string | null, districtWide?: boolean) => void
    Disabled?: boolean
    HasError?: boolean
    Label?: string
    Multi?: boolean
    Required?: boolean
    Variant?: 'outlined' | 'standard'
    ShowDistrictWideSwitch?: boolean
    AvailableSiteIds?: string[] // limit available sites to select
    DistrictWide?: boolean
}

// should be in content
export const SiteSelectorForContent = ({
    Selected,
    OnChange = () => {},
    ContentType,
    ParentSitesIDs,
    Disabled = false,
    HasError = false,
    IgnoreSiteID = false,
    Label = 'Sites',
    Multi = true,
    Required = false,
    Variant = 'outlined',
    ShowDistrictWideSwitch,
    AvailableSiteIds,
    DistrictWide = false
}: SiteSelectorForContentProps) => {
    const evaluators = useAppContext()
    const currentSite = useCurrentSite()
    const sitesForContent = useSitesForContent({
        ContentType: typeToEntityScope(ContentType),
        ParentSitesIDs: ParentSitesIDs,
        IgnoreSiteID
    })
    const [sites, setSites] = useState<SitesForSelector | undefined>(undefined)
    const availableSites: SitesForSelector | null = AvailableSiteIds
        ? sites?.filter((s) => AvailableSiteIds.includes(s.ID)) || []
        : null

    const [selected, setSelected] = useState<string[]>(Selected || evaluators.getDefaultSitesForSelectors())
    const { data: tags } = useTagsQueryV2({
        Search: '',
        Types: [TagType.Site]
    })
    const serverTags = useMemo(() => tags?.Rows?.map((tag) => ({ name: tag.Name, id: tag.ID })) || [], [tags])

    const [districtWideSwitchIsEnabled, setDistrictWideSwitchIsEnabled] = useState(false)

    useEffect(() => {
        updateValue(Selected || evaluators.getDefaultSitesForSelectors())
    }, [Selected])

    const updateValue = (v: string[]) => {
        if (selected.length === 0 && v.length === 0) {
            return
        }
        setSelected(v)
        OnChange(v, currentSite?.Type === 'department' ? currentSite.ID : null)
    }

    const updateSites = (s: SitesForSelector) => {
        const newSites = s.sort((a, b) => a.Name.localeCompare(b.Name))
        setSites(newSites)
    }

    useEffect(() => {
        if (sitesForContent.length) {
            updateSites(sitesForContent)
        }
    }, [ContentType, ParentSitesIDs])

    return !Array.isArray(sites) || sites.length === 0 ? null : (
        <div style={{ width: '100%' }}>
            <FormControl sx={{ width: '100%', display: 'flex' }} variant={Variant}>
                <InputLabel required={Required}>{Label}</InputLabel>
                <Select
                    error={Boolean(HasError)}
                    disabled={Boolean(Disabled) || districtWideSwitchIsEnabled === true}
                    multiple={Multi}
                    value={selected}
                    onChange={(e) => {
                        updateValue(e.target.value as string[])
                    }}
                    input={Variant == 'outlined' ? <OutlinedInput /* label="Sites" */ label={Label} /> : undefined}
                    renderValue={(selected) =>
                        sites
                            ?.filter((s) => selected.indexOf(s.ID) > -1)
                            .map((s) => s.Name)
                            .join(', ')
                    }
                    // MenuProps={MenuProps}
                >
                    {(availableSites || sites).map((site) => (
                        <MenuItem key={site.ID} value={site.ID}>
                            {Multi && <Checkbox checked={selected.indexOf(site.ID) > -1} />}
                            <ListItemText primary={site.Name} />
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Box sx={{ display: 'flex' }}>
                {Multi && (
                    <FormControl>
                        <TagsSelectorForSites
                            sites={sites}
                            selectedSiteIDs={selected}
                            serverTags={serverTags}
                            onChange={(command, siteIDs) => {
                                if (command === 'select') {
                                    updateValue(Array.from(new Set([...selected, ...siteIDs])))
                                } else {
                                    updateValue(selected.filter((s) => siteIDs.indexOf(s) === -1))
                                }
                            }}
                            disabled={Boolean(Disabled) || districtWideSwitchIsEnabled === true}
                        />
                    </FormControl>
                )}
                {ShowDistrictWideSwitch && (
                    <FormControlLabel
                        sx={{ alignSelf: 'flex-start' }}
                        disabled={Disabled}
                        label={'District Wide'}
                        control={
                            <Switch
                                disabled={Disabled}
                                checked={DistrictWide}
                                onChange={(e) => {
                                    setDistrictWideSwitchIsEnabled(e.target.checked)
                                    if (e.target.checked) {
                                        OnChange?.([], null, true)
                                    } else {
                                        OnChange?.(
                                            selected || [],
                                            currentSite?.Type === 'department' ? currentSite.ID : null,
                                            false
                                        )
                                    }
                                }}
                            />
                        }
                    />
                )}
            </Box>
        </div>
    )
}

type SiteSelectorForPreviewParams = {
    contentID: string
    parentSitesIDs?: string[] // parent site IDs
    contentType: string
    onClick?: () => void
}
export const SiteSelectorForPreview = ({
    contentID,
    contentType,
    parentSitesIDs,
    onClick = () => {}
}: SiteSelectorForPreviewParams) => {
    console.log('parentSitesIDs', parentSitesIDs)
    const currentSite = useCurrentSite()
    const sitesForContent = useSitesForContent({
        ContentType: contentType,
        ParentSitesIDs: parentSitesIDs
    })

    const [sites, setSites] = useState<SitesForSelector | undefined>(undefined)
    const [loading, setLoading] = useState<boolean>(false)

    const updateSites = (s: SitesForSelector) => {
        setSites(s.sort((a, b) => a.Name.localeCompare(b.Name)))
        setLoading(false)
    }

    useEffect(() => {
        if (!currentSite || !currentSite.ID || !contentType) return
        updateSites(sitesForContent)
    }, [currentSite, contentType, parentSitesIDs, sitesForContent])

    return loading ? (
        <p>Loading...</p>
    ) : (
        <ul>
            {sites?.map((site) => (
                <li key={site.ID}>
                    <a
                        href={`${BASE}/api/v2/content/preview/${contentID}?previewSiteID=${site.ID}`}
                        target='_blank'
                        onClick={onClick}
                    >
                        {site.Name}
                    </a>
                </li>
            ))}
        </ul>
    )
}

async function handlePreview(
    this: { openDialog: (content: contentLike) => Promise<void>; currentSite: Site | undefined },
    state: contentLike
) {
    if (isRouteExternal(state.route)) {
        return window.open(state.route)
    }
    if (this.currentSite?.Type === 'department') {
        return await this.openDialog(state)
    }

    return window.open(
        `${BASE}/api/v2/content/preview/${state.id}/${state.Workspace}?previewSiteID=${this.currentSite?.ID}`,
        '_blank'
    )
}

const handlePreviewContextAtom = atom({
    openDialog: (content: contentLike) => Promise.resolve(),
    currentSite: undefined as Site | undefined
})
export const handlePreviewAtom = atom((get) => handlePreview.bind(get(handlePreviewContextAtom)))

type contentLike = { id: string; Workspace: string; type: string; sites: string[]; route: string }
export const PreviewForDepartmentDialogAlt = () => {
    const [content, setContent] = useState<{ id: string; type: string; sites: string[] } | undefined>(undefined)
    const [open, setOpen] = useState<boolean>(false)
    const currentSite = useCurrentSite()
    const [, setHandlePreviewContext] = useAtom(handlePreviewContextAtom)
    const resolve = () => {}
    const reject = () => {}

    const openDialog = (content: contentLike) => {
        if (!content) return Promise.resolve()
        setContent(content)
        setOpen(true)
        return new Promise<void>((res, rej) => {
            res = resolve
            rej = reject
        })
    }
    useEffect(() => {
        if (!currentSite) return
        try {
            setHandlePreviewContext({ openDialog, currentSite })
        } catch (e) {
            console.error(e)
        }
    }, [currentSite])

    const secured = asSecured(content)
    return (
        <Dialog
            open={open}
            onClose={() => {
                setOpen(false)
                resolve()
            }}
        >
            <DialogTitle>Select a site for preview</DialogTitle>
            <DialogContent>
                {content && (
                    <SiteSelectorForPreview
                        contentID={content.id}
                        contentType={secured.EntityScope}
                        parentSitesIDs={content.sites}
                        onClick={() => {
                            setOpen(false)
                            resolve()
                        }}
                    />
                )}
            </DialogContent>
        </Dialog>
    )
}

export const PreviewMenuItem = ({ content, onClick = () => {} }: { content: contentLike; onClick: () => void }) => {
    const [handlePreview] = useAtom(handlePreviewAtom)
    const handle = () => {
        handlePreview(content)
        onClick()
    }
    return (
        <CustomMenuItem text={'Preview'} onClick={handle}>
            <VisibilityIcon />
        </CustomMenuItem>
    )
}
