import { createTheme } from '@mui/material'
import { renderSelectedValue } from '../helpers'
import { alpha, PaletteColorOptions, Theme } from '@mui/material/styles'
import { colours } from '@/common/colours'

// required to support theme.spacing()
// https://stackoverflow.com/questions/70697666/material-ui-v5-property-spacing-does-not-exist-on-type-defaulttheme-when-u
declare module '@mui/styles/defaultTheme' {
    interface DefaultTheme extends Theme {}
}

declare module '@mui/material/styles' {
    interface Palette {
        department: PaletteColorOptions
    }

    interface PaletteOptions {
        department: PaletteColorOptions
    }
}
const SELECT_ITEM_HEIGHT = 48
const SELECT_ITEM_PADDING_TOP = 8
export const mainBorderRadius = '8px'

export const primaryTheme = createTheme({
    palette: {
        mode: 'light',
        primary: {
            main: '#4c84ff'
        },
        secondary: {
            main: '#FF7F66'
        },
        error: {
            main: '#f44336'
        },
        warning: {
            main: '#ffb900'
        },
        department: {
            main: '#4E01D5'
        }
    },
    typography: {
        // fontFamily: 'montserrat',
        fontWeightLight: 300,
        h2: {
            fontWeight: 600
        },
        h1: {
            fontWeight: 600
        },
        h3: {
            fontWeight: 700
        },
        h4: {
            fontWeight: 600
        },
        h5: {
            fontWeight: 600
        },
        h6: {
            fontWeight: 700
        },
        subtitle1: {
            fontWeight: 600
        },
        button: {
            fontWeight: 600,
            textTransform: undefined
        }
    },
    components: {
        MuiDialog: {
            styleOverrides: {
                paper: {
                    boxShadow: '0px 5px 10px rgba(0, 0, 0, 0.3)',
                    borderRadius: '8px',
                    transition: 'opacity 0.5s'
                }
            }
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    // backgroundColor: "#212a39",
                    // backgroundColor: '#233350'
                    backgroundColor: '#191970'
                }
            }
        },
        MuiSelect: {
            defaultProps: {
                renderValue: renderSelectedValue,
                MenuProps: {
                    PaperProps: {
                        style: {
                            maxHeight: SELECT_ITEM_HEIGHT * 4.5 + SELECT_ITEM_PADDING_TOP,
                            width: 250
                        }
                    }
                }
            },
            styleOverrides: {
                root: {
                    width: '100%'
                }
            } as any
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    textTransform: 'uppercase',
                    borderRadius: '4px'
                },
                outlinedError: {
                    borderWidth: '2px',
                    borderColor: colours.error,
                    ':hover': {
                        borderWidth: '2px',
                        backgroundColor: alpha(colours.error, 0.15)
                    }
                },
                outlinedPrimary: {
                    borderWidth: '2px',
                    borderColor: colours.base_blue,
                    ':hover': {
                        borderWidth: '2px',
                        backgroundColor: alpha(colours.base_blue, 0.15)
                    }
                },
                outlinedInherit: {
                    borderWidth: '2px'
                }
            }
        },
        MuiFormControl: {
            styleOverrides: {
                root: {
                    '& .MuiFormLabel-root': {
                        fontWeight: '500'
                    }
                }
            },
            defaultProps: {
                variant: 'outlined'
            }
        },
        MuiFormControlLabel: {
            styleOverrides: {
                label: {
                    fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
                    fontSize: '14px'
                }
            }
        },
        MuiAutocomplete: {
            styleOverrides: {
                root: {
                    width: '100%'
                }
            }
        },
        MuiTextField: {
            styleOverrides: {
                root: {
                    width: '100%'
                }
            },
            defaultProps: {
                variant: 'outlined',
                fullWidth: true,
                InputLabelProps: { shrink: true }
            }
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    borderRadius: '4px',
                    position: 'relative',
                    fontSize: 16,
                    width: '100%'
                }
            },
            defaultProps: {
                notched: true
            }
        },

        MuiInputLabel: {
            defaultProps: {
                shrink: true
            }
        },
        MuiInputBase: {
            styleOverrides: {
                root: {
                    '& input': {
                        '&:focus': {}
                    }
                }
            }
        },
        MuiTooltip: {
            defaultProps: {
                arrow: true
            }
        }
    },
    spacing: 8,
    shape: {
        borderRadius: 2
    }
})

// palette: {
//     type: 'dark',
//     primary: {
//         main: '#2a2f4e',
//         light: '#45485e',
//         dark: '#111320',
//         contrastText: '#ffffff',
//     },
//     secondary: {
//         main: '#e69806',
//         light: '#efb23f',
//         dark: '#7f5303',
//         contrastText: 'rgba(245,242,242,0.87)',
//     },
//     error: {
//         main: '#c5291e',
//         light: '#ce5149',
//         dark: '#8a1c15',
//         contrastText: '#f7f2f2',
//     },
// },
// components: {
//     MuiButton: {
//         defaultProps: {
//             // color:"secondary"
//         }
//     }
// }

// import { ThemeOptions } from '@material-ui/core/styles/createMuiTheme';
//
// export const themeOptions: ThemeOptions = {
//     palette: {
//         type: 'light',
//         primary: {
//             main: '#4c84ff',
//         },
//         secondary: {
//             main: '#FF7F66',
//         },
//         error: {
//             main: '#f44336',
//         },
//         warning: {
//             main: '#ffb900',
//         },
//     },
//     typography: {
//         fontFamily: 'montserrat',
//         fontWeightLight: 300,
//         h2: {
//             fontWeight: 600,
//         },
//         h1: {
//             fontWeight: 600,
//         },
//         h3: {
//             fontWeight: 700,
//         },
//         h4: {
//             fontWeight: 600,
//         },
//         h5: {
//             fontWeight: 600,
//         },
//         h6: {
//             fontWeight: 700,
//         },
//         subtitle1: {
//             fontWeight: 600,
//         },
//         button: {
//             fontWeight: 600,
//             textTransform: null,
//         },
//     },
//     overrides: {
//         MuiAppBar: {
//             colorInherit: {
//                 backgroundColor: '#212a39',
//                 color: '#fff',
//             },
//         },
//     },
//     props: {
//         MuiAppBar: {
//             color: 'inherit',
//         },
//         MuiTooltip: {
//             arrow: true,
//         },
//     },
//     spacing: 8,
//     shape: {
//         borderRadius: 2,
//     },
// };
