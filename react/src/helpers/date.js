import moment from 'moment'

// const isToTimeStringRegex = /([0-9]{2}):([0-9]{2}):([0-9]{2}) (GMT)(([+-])[0-9]{4}) (\()([a-zA-Z ]{0,})(\))+/g
const timeRegex = /([0-9]{2}:[0-9]{2}:[0-9]{2})+/g
const isToTimeStringRegex = /([0-9]{2}:[0-9]{2}:[0-9]{2} GMT[+-][0-9]{4} \([a-zA-Z ]{0,}\))+/g
export const Thursday = 4

export const millisecondsToDays = (milliseconds) => {
    return milliseconds / (1000 * 60 * 60 * 24)
}
export const daysBetweenDates = (fromDate, toDate) => {
    const diff = new Date(toDate).getTime() - new Date(fromDate).getTime()
    return millisecondsToDays(diff)
}
export const isDateGreater = (date, compareTo) => {
    return moment(date).isAfter(moment(compareTo))
}

export function isToday(test) {
    if (!(test instanceof Date)) {
        test = new Date(test)
    }
    const today = new Date()
    return (
        today.getDate() === test.getDate() &&
        today.getMonth() === test.getMonth() &&
        today.getFullYear() === test.getFullYear()
    )
}

export function getDaysForMonth(day, month) {
    const days = []
    const d = new Date()

    if (month) {
        d.setMonth(month, 1)
        month = month % 12
    } else {
        month = d.getMonth()
    }
    d.setDate(1)
    while (d.getDay() !== day) {
        d.setDate(d.getDate() + 1)
    }
    while (d.getMonth() === month) {
        days.push(new Date(d.getTime()))
        d.setDate(d.getDate() + 7)
    }
    return days
}

export function getNextDate(dayNumber, interval, month) {
    let date = dateHelpers.getDaysForMonth(dayNumber, month)?.[interval]
    if (!date) return ''
    if (dateHelpers.isDateGreater(new Date(), date)) {
        date = dateHelpers.getDaysForMonth(dayNumber, new Date().getMonth() + 1)?.[interval]
    }
    return new Date(date)
}

export function renderNextThirdThursday(skipDates) {
    let date = dateHelpers.getNextDate(Thursday, 2)
    let fmtDate = moment(date).format('LL')

    for (let i = 0; i < skipDates?.length; i++) {
        if (moment(skipDates[i]).isSame(fmtDate)) {
            date = dateHelpers.getNextDate(Thursday, 2, moment(skipDates[i]).month() + 1)
            fmtDate = moment(date).format('LL')
        }
    }

    if (dateHelpers.isToday(fmtDate)) {
        fmtDate = `Today, ${fmtDate}`
    }
    return fmtDate
}

// export function utc(dateLike) {
//     return moment(dateLike).tz('UTC')
// }

export function isDate(dateLike) {
    try {
        return !isNaN(Date.parse(dateLike || ''))
    } catch {
        return false
    }
}

export function isDateTimeStringFormat(dateLike) {
    return isToTimeStringRegex.test(String(dateLike || ''))
}

export const dateHelpers = {
    isDate,
    isDateTimeStringFormat,
    // utc,
    isToday,
    isDateGreater,
    millisecondsToDays,
    daysBetweenDates,
    getDaysForMonth,
    getNextDate
}
