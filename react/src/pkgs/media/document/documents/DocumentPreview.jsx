import React, { Fragment, useEffect, useState } from 'react'
import { documentService } from '../document.service'
import { useCurrentSiteID } from '../../../auth/atoms'
import { Alert } from '@mui/material'
import { guessErrorMessage } from '../../../../helpers/guessErrorMessage'
import CenteredSpinner from '../../../../common/components/CenteredSpinner'

const DocumentPreview = ({ documentId, editedBinary }) => {
    const currentSiteID = useCurrentSiteID()
    const [documentBinary, setDocumentBinary] = useState('')
    const [isLoading, setIsLoading] = useState(true)
    const [errorMsg, setErrorMsg] = useState('')

    useEffect(() => {
        if (documentId) {
            documentService.getPDF(currentSiteID, documentId).then(
                (r) => {
                    setDocumentBinary(r)
                    setIsLoading(false)
                },
                (e) => setErrorMsg(guessErrorMessage(e))
            )
        }
    }, [documentId])

    useEffect(() => {
        if (!editedBinary || !editedBinary.length) {
            return
        }
        setDocumentBinary(editedBinary)
    }, [editedBinary])

    if (isLoading) {
        return <CenteredSpinner />
    }

    if (errorMsg) {
        return <Alert severity='error'>{errorMsg}</Alert>
    }

    return (
        <Fragment>
            <iframe style={{ width: '100%', height: '100%' }} src={documentBinary} title='Document' />
        </Fragment>
    )
}
export default DocumentPreview
