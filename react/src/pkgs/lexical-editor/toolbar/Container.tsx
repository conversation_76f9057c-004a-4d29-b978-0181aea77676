import React, { forwardRef, ReactNode } from 'react'
import { Box, SxProps } from '@mui/material'
import { useAtom } from 'jotai/index'
import { appTopBarHeightAtom } from '@/app/AppTopBar/AppTopBar'
import { bannerHeightAtom } from '@/app/AppTopBar/InstagramErrorsBanner'
import { containerEditorSaveBarHeightAtom } from '@/pkgs/content/editor/ContentEditorSaveBar'
import { colours } from '@/common/colours'

export const STICKY_TOOLBAR_ZINDEX = 1000
export const Container = forwardRef(
    (
        {
            sx,
            children,
            className
        }: {
            sx?: SxProps
            children: ReactNode
            className?: string
        },
        ref
    ) => {
        // @ts-ignore
        const toolbarRef = ref?.current as HTMLDivElement
        const isInsideDialog = !!toolbarRef?.closest('.MuiDialogContent-root')
        const [appTopBarHeight] = useAtom(appTopBarHeight<PERSON>tom)
        const [bannerHeight] = useAtom(bannerHeightAtom)
        const [containerEditorSaveBarHeight] = useAtom(containerEditorSaveBarHeightAtom)

        return (
            // @ts-ignore sx prop type error
            <Box
                data-testid='lexical-toolbar'
                ref={ref}
                sx={{
                    '& .material-icons-outlined': {
                        fontSize: '18px'
                    },
                    flexWrap: 'wrap',
                    borderBottom: `1px solid ${colours.off_white_but_darker}`,
                    position: 'sticky !important',
                    zIndex: STICKY_TOOLBAR_ZINDEX,
                    top: isInsideDialog
                        ? '-20px'
                        : `${bannerHeight + appTopBarHeight + containerEditorSaveBarHeight}px`,
                    display: 'flex',
                    marginBottom: '1px',
                    backgroundColor: colours.white,
                    padding: '4px',
                    borderTopLeftRadius: '10px',
                    borderTopRightRadius: '10px',
                    verticalAlign: 'middle',
                    ...sx
                }}
                className={className || ''}
            >
                <>{children}</>
            </Box>
        )
    }
)
