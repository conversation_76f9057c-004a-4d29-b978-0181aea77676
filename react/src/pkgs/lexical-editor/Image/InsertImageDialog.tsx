import { LexicalEditor } from 'lexical'
import { ImageGallery } from '../../media/image/ImageGallery'
import { useEffect, useRef } from 'react'
import { INSERT_IMAGE_COMMAND, InsertImagePayload } from './ImagesPlugin'
import { Image } from '@/pkgs/storage/queries'
import { getCropSizeFromCropId } from '@/pkgs/storage/image_crop/cropPathUtils'
import { useImageCropSizeQuery } from '@/pkgs/system/image-crop-size/queries'

interface InsertImageDialogProps {
    activeEditor: LexicalEditor
    isOpen: boolean
    onClose: () => void
    allowedCropSizes?: string[]
}

export function InsertImageDialog({
    activeEditor,
    isOpen,
    onClose,
    allowedCropSizes
}: InsertImageDialogProps): JSX.Element {
    const { data: imageCropSizes } = useImageCropSizeQuery(!!allowedCropSizes?.length)
    const hasModifier = useRef(false)

    useEffect(() => {
        hasModifier.current = false
        const handler = (e: KeyboardEvent) => {
            hasModifier.current = e.altKey
        }
        document.addEventListener('keydown', handler)
        return () => {
            document.removeEventListener('keydown', handler)
        }
    }, [activeEditor])

    const onClick = (image: Image) => {
        const cropName = getCropSizeFromCropId(image.id)
        const cropSize = imageCropSizes?.Rows?.find((s) => s.Name == cropName)

        const payload: InsertImagePayload = {
            altText: image?.alt || '',
            src: `/images/${image.id}`,
            captionsEnabled: true,
            width: cropSize?.Width,
            height: cropSize?.Height
        }
        activeEditor.dispatchCommand(INSERT_IMAGE_COMMAND, payload)
        onClose()
    }

    return (
        <>
            {isOpen && (
                <ImageGallery open={isOpen} close={onClose} onChange={onClick} allowedCropSizes={allowedCropSizes} />
            )}
        </>
    )
}
