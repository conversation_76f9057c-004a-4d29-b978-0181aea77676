/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type { BaseSelection, LexicalCommand, LexicalEditor, NodeKey } from 'lexical'
import {
    $getNodeByKey,
    $getSelection,
    $isNodeSelection,
    $isRangeSelection,
    $setSelection,
    CLICK_COMMAND,
    COMMAND_PRIORITY_LOW,
    createCommand,
    DRAGSTART_COMMAND,
    KEY_BACKSPACE_COMMAND,
    KEY_DELETE_COMMAND,
    KEY_ENTER_COMMAND,
    KEY_ESCAPE_COMMAND,
    SELECTION_CHANGE_COMMAND
} from 'lexical'

import './ImageNode.css'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { useLexicalNodeSelection } from '@lexical/react/useLexicalNodeSelection'
import { mergeRegister } from '@lexical/utils'
import * as React from 'react'
import { Suspense, useCallback, useEffect, useRef, useState } from 'react'

import { $isImageNode } from './ImageNode'
import { ImageResizer } from './ImageResizer'
import { FloatingImageMenu } from './FloatingImageMenu'
import { FloatingImageLinkEditor } from './FloatingImageLinkEditor'
import { FloatingImageCaptionEditor } from './FloatingImageCaptionEditor'
import { parseInlineStyle } from '@/pkgs/lexical-editor/Image/parseInlineStyle'
import { LinkAttributes } from '../Link/LinkNode'

const imageCache = new Set()

export const RIGHT_CLICK_IMAGE_COMMAND: LexicalCommand<MouseEvent> = createCommand('RIGHT_CLICK_IMAGE_COMMAND')

function useSuspenseImage(src: string) {
    if (!imageCache.has(src)) {
        throw new Promise((resolve) => {
            const img = new Image()
            img.src = src
            img.onload = () => {
                imageCache.add(src)
                resolve(null)
            }
        })
    }
}

interface LazyImageProps {
    altText: string
    className: string | null
    height: 'inherit' | number
    imageRef: { current: null | HTMLImageElement }
    maxWidth: string | number
    src: string
    width: 'inherit' | number | string
    style?: string
}

function LazyImage({ altText, className, imageRef, src, width, height, maxWidth, style }: LazyImageProps): JSX.Element {
    // SVGs will not display if width/height not explicitly defined
    const _width = width == 'inherit' ? '100%' : width

    const styleObject = parseInlineStyle(style)
    styleObject['maxWidth'] = maxWidth

    return (
        <img
            className={className || undefined}
            src={src}
            alt={altText}
            ref={imageRef}
            width={_width}
            height={height}
            style={styleObject}
            draggable='false'
        />
    )
}

interface ImageComponentProps {
    altText: string
    caption: string
    height: 'inherit' | number
    maxWidth: string | number
    nodeKey: NodeKey
    resizable: boolean
    showCaption: boolean
    src: string
    width: 'inherit' | number | string
    captionsEnabled: boolean
    href: string
    target: string | undefined
    style?: string
}

export default function ImageComponent({
    src,
    altText,
    nodeKey,
    width,
    height,
    maxWidth,
    resizable,
    showCaption,
    caption,
    captionsEnabled,
    href,
    target,
    style
}: ImageComponentProps): JSX.Element {
    const imageRef = useRef<null | HTMLImageElement>(null)
    const [isSelected, setSelected, clearSelection] = useLexicalNodeSelection(nodeKey)
    const [isResizing, setIsResizing] = useState<boolean>(false)
    const [editor] = useLexicalComposerContext()
    const [selection, setSelection] = useState<BaseSelection | null>(null)
    const activeEditorRef = useRef<LexicalEditor | null>(null)
    const [imageLinkEditorIsVisible, setImageLinkEditorIsVisible] = useState(false)
    const imageCaptionTextFieldRef = useRef<HTMLDivElement | null>(null)
    const [currentHref, setCurrentHref] = useState(href)
    const [currentTarget, setCurrentTarget] = useState(target)

    const currentFloat = parseInlineStyle(style)['float']

    const onDelete = useCallback(
        (payload: KeyboardEvent) => {
            if (isSelected && $isNodeSelection($getSelection())) {
                const event: KeyboardEvent = payload
                event.preventDefault()
                const node = $getNodeByKey(nodeKey)
                if ($isImageNode(node)) {
                    node.remove()
                }
            }
            return false
        },
        [isSelected, nodeKey]
    )

    const onEnter = useCallback(
        (event: KeyboardEvent) => {
            const latestSelection = $getSelection()
            if (isSelected && $isNodeSelection(latestSelection) && latestSelection.getNodes().length === 1) {
                if (showCaption) {
                    event.preventDefault()
                    imageCaptionTextFieldRef.current?.focus()
                    return true
                }
            }
            return false
        },
        [caption, isSelected, showCaption]
    )

    const onEscape = useCallback(
        (event: KeyboardEvent) => {
            if (imageCaptionTextFieldRef.current === event.target) {
                $setSelection(null)
                editor.update(() => {
                    setSelected(true)
                    const parentRootElement = editor.getRootElement()
                    if (parentRootElement !== null) {
                        parentRootElement.focus()
                    }
                })
                return true
            }
            return false
        },
        [caption, editor, setSelected]
    )

    const onClick = useCallback(
        (payload: MouseEvent) => {
            const event = payload

            if (isResizing) {
                return true
            }
            if (event.target === imageRef.current) {
                if (event.shiftKey) {
                    setSelected(!isSelected)
                } else {
                    clearSelection()
                    setSelected(true)
                }
                return true
            }

            return false
        },
        [isResizing, isSelected, setSelected, clearSelection]
    )

    const onRightClick = useCallback(
        (event: MouseEvent): void => {
            editor.getEditorState().read(() => {
                const latestSelection = $getSelection()
                const domElement = event.target as HTMLElement
                if (
                    domElement.tagName === 'IMG' &&
                    $isRangeSelection(latestSelection) &&
                    latestSelection.getNodes().length === 1
                ) {
                    editor.dispatchCommand(RIGHT_CLICK_IMAGE_COMMAND, event as MouseEvent)
                }
            })
        },
        [editor]
    )

    useEffect(() => {
        let isMounted = true
        const rootElement = editor.getRootElement()
        const unregister = mergeRegister(
            editor.registerUpdateListener(({ editorState }) => {
                if (isMounted) {
                    setSelection(editorState.read(() => $getSelection()))
                }
            }),
            editor.registerCommand(
                SELECTION_CHANGE_COMMAND,
                (_, activeEditor) => {
                    activeEditorRef.current = activeEditor
                    return false
                },
                COMMAND_PRIORITY_LOW
            ),
            editor.registerCommand<MouseEvent>(CLICK_COMMAND, onClick, COMMAND_PRIORITY_LOW),
            editor.registerCommand<MouseEvent>(RIGHT_CLICK_IMAGE_COMMAND, onClick, COMMAND_PRIORITY_LOW),
            editor.registerCommand(
                DRAGSTART_COMMAND,
                (event) => {
                    if (event.target === imageRef.current) {
                        // TODO This is just a temporary workaround for FF to behave like other browsers.
                        // Ideally, this handles drag & drop too (and all browsers).
                        event.preventDefault()
                        return true
                    }
                    return false
                },
                COMMAND_PRIORITY_LOW
            ),
            editor.registerCommand(KEY_DELETE_COMMAND, onDelete, COMMAND_PRIORITY_LOW),
            editor.registerCommand(KEY_BACKSPACE_COMMAND, onDelete, COMMAND_PRIORITY_LOW),
            editor.registerCommand(KEY_ENTER_COMMAND, onEnter, COMMAND_PRIORITY_LOW),
            editor.registerCommand(KEY_ESCAPE_COMMAND, onEscape, COMMAND_PRIORITY_LOW)
        )

        rootElement?.addEventListener('contextmenu', onRightClick)

        return () => {
            isMounted = false
            unregister()
            rootElement?.removeEventListener('contextmenu', onRightClick)
        }
    }, [
        clearSelection,
        editor,
        isResizing,
        isSelected,
        nodeKey,
        onDelete,
        onEnter,
        onEscape,
        onClick,
        onRightClick,
        setSelected
    ])

    function updateImageFloat(float: 'left' | 'right' | null | undefined) {
        editor.update(() => {
            const node = $getNodeByKey(nodeKey)
            if ($isImageNode(node)) {
                node.setFloat(float)
            }
        })
    }

    function toggleShowCaption() {
        editor.update(() => {
            const node = $getNodeByKey(nodeKey)
            if ($isImageNode(node)) {
                node.setShowCaption(!node.__showCaption)
            }
        })
    }

    const updateLink = useCallback(
        (href: string, linkAttributes?: LinkAttributes) => {
            editor.update(() => {
                const node = $getNodeByKey(nodeKey)
                if ($isImageNode(node)) {
                    node.setHref(href)
                    node.setTarget(linkAttributes?.target || undefined)
                    setCurrentTarget(linkAttributes?.target || undefined)
                    setCurrentHref(href)
                }
            })
        },
        [editor]
    )

    const onResizeEnd = (nextWidth: 'inherit' | number, nextHeight: 'inherit' | number) => {
        // Delay hiding the resize bars for click case
        setTimeout(() => {
            setIsResizing(false)
        }, 200)

        editor.update(() => {
            const node = $getNodeByKey(nodeKey)
            if ($isImageNode(node)) {
                node.setWidthAndHeight(nextWidth, nextHeight)
            }
        })
    }

    function onCaptionChange(caption: string) {
        editor.update(() => {
            const node = $getNodeByKey(nodeKey)
            if ($isImageNode(node)) {
                node.setCaption(caption)
            }
        })
    }

    const onResizeStart = () => {
        setIsResizing(true)
    }

    const draggable = isSelected && $isNodeSelection(selection) && !isResizing
    const isFocused = isSelected || isResizing

    useEffect(() => {
        if (!isSelected) {
            setImageLinkEditorIsVisible(false)
        }
    }, [isSelected])

    return (
        <Suspense fallback={null}>
            <>
                <div draggable={draggable}>
                    {showCaption && isFocused && (
                        <FloatingImageCaptionEditor
                            imageCaptionTextFieldRef={imageCaptionTextFieldRef}
                            imageRef={imageRef}
                            value={caption}
                            onChangeHandler={(val) => onCaptionChange(val)}
                        />
                    )}
                    <LazyImage
                        className={isFocused ? `focused ${$isNodeSelection(selection) ? 'draggable' : ''}` : null}
                        src={src}
                        altText={altText}
                        imageRef={imageRef}
                        width={width}
                        height={height}
                        maxWidth={maxWidth}
                        style={style}
                    />
                </div>

                {resizable && $isNodeSelection(selection) && isFocused && (
                    <ImageResizer
                        showCaption={showCaption}
                        editor={editor}
                        imageRef={imageRef}
                        maxWidth={maxWidth}
                        onResizeStart={onResizeStart}
                        onResizeEnd={onResizeEnd}
                        captionsEnabled={captionsEnabled}
                    />
                )}

                {$isNodeSelection(selection) && isFocused && (
                    <FloatingImageMenu
                        editor={editor}
                        imageRef={imageRef}
                        anchorElem={document.body}
                        captionsOnClickHandler={() => {
                            toggleShowCaption()
                        }}
                        linkOnClickHandler={() => setImageLinkEditorIsVisible(!imageLinkEditorIsVisible)}
                        showCaption={showCaption}
                        linkExists={!!currentHref}
                        resetSizeHandler={() => {
                            const image = imageRef.current
                            if (!image) return

                            image.style.width = '100%'
                            image.style.height = 'inherit'
                            image.style.maxWidth = 'inherit'
                            image.setAttribute('width', '100%')
                            image.setAttribute('height', 'auto')
                            editor.update(() => {
                                const node = $getNodeByKey(nodeKey)
                                if ($isImageNode(node)) {
                                    node.setWidthAndHeight('100%', 'inherit')
                                }
                            })
                        }}
                        currentFloat={currentFloat}
                        wrapTextHandler={(float) => {
                            if (currentFloat == float) {
                                updateImageFloat(null)
                            } else {
                                updateImageFloat(float)
                            }
                        }}
                    />
                )}
                {$isNodeSelection(selection) && isFocused && imageLinkEditorIsVisible && (
                    <FloatingImageLinkEditor
                        editor={editor}
                        defaultLink={currentHref}
                        defaultAttributes={{
                            target: currentTarget
                        }}
                        imageRef={imageRef}
                        linkOnChangeHandler={(link, linkAttributes) => updateLink(link, linkAttributes)}
                    />
                )}
            </>
        </Suspense>
    )
}
