import { Item } from '@/pkgs/search/promotions/types'
import { useAppContext } from '@/pkgs/auth/atoms'
import { useBatchedContentDetails } from '@/pkgs/content/queries'
import { useQuery } from '@tanstack/react-query'
import { baseQueryConfig } from '@/common/react-query'
import { httpGet } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'

export const usePreview = (item: Item) => {
    const appContext = useAppContext()
    const contentResult = useBatchedContentDetails(item.ExtID && item.Type !== 'document' ? item.ExtID : undefined)
    const documentResult = useQuery({
        ...baseQueryConfig,
        queryKey: ['document-details', item.ExtID],
        enabled: !!item.ExtID && item.Type === 'document',
        queryFn: async () =>
            await httpGet(
                `${BASE}/api/v1/document/${item.ExtID}`,
                null,
                z.object({
                    id: z.string(),
                    filename: z.string(),
                    sites: z.array(z.string()),
                    privacyLevel: z.number()
                })
            )
    })

    let data = {
        Item: item,
        PreviewLink: '',
        EditLink: '',
        Title: '',
        Description: '',
        PrivacyLevel: 0
    }

    if (!item.ExtID) {
        data.PreviewLink = item.Link || ''
        data.EditLink = ''
        data.Title = item.Title
        data.Description = item.Description
        //
    } else if (documentResult.data) {
        data.PreviewLink = `https://${appContext.getBestSite(documentResult.data.sites)?.PrimaryDomain}/documents/${
            item.ExtID
        }/${documentResult.data.filename}`
        data.EditLink = ''
        data.Title = documentResult.data.filename
        data.Description = ''
        data.PrivacyLevel = documentResult.data.privacyLevel
        //
    } else if (contentResult.data) {
        if (contentResult.data.Route.startsWith('http')) {
            data.PreviewLink = contentResult.data.Route
        } else {
            const site = appContext.getBestSite(contentResult.data.Sites)
            data.PreviewLink = !!site ? `https://${site.PrimaryDomain}${contentResult.data.Route}` : ''
        }
        data.EditLink = `/content-editor/${item.ExtID}`
        data.Title = contentResult.data.Title
        data.PrivacyLevel = contentResult.data.PrivacyLevel
    }

    return {
        isLoading: !item.ExtID ? false : item.Type === 'document' ? documentResult.isLoading : contentResult.isLoading,
        isError: !item.ExtID ? false : item.Type === 'document' ? documentResult.isError : contentResult.isError,
        error: !item.ExtID ? null : item.Type === 'document' ? documentResult.error : contentResult.error,
        data
    }
}
