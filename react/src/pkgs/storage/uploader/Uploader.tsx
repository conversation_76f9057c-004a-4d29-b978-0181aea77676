import { FileSelector } from '@/pkgs/storage/uploader/FileSelector'
import React, { useState } from 'react'
import { FileCard } from '@/pkgs/storage/uploader/FileCard'
import CMDialog from '@/common/components/CMDialog'
import { Button, DialogContent, FormControl, Grid } from '@mui/material'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'
import { Base } from '@/pkgs/content/types'
import { Structure } from '@/pkgs/structure/types'
import { StructureSelector } from '@/pkgs/structure/StructureSelector'
import { BaseForm } from '@/pkgs/content/BaseForm'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { TagType } from '@/pkgs/system/tags/types'

type UploaderProps = {
    type: 'image' | 'document'
    multiple: boolean
    onError?: (error: string, file: File) => void
    disabled?: boolean
    sizeLimit?: number
    buttonText?: string
}

export const Uploader = ({ type, multiple, onError, disabled, sizeLimit }: UploaderProps) => {
    const [files, setFiles] = useState<File[]>([])
    const { data: cdi } = useURLParamMenuItem()
    const [defaultBase, setDefaultBase] = useState<Base>({ PublishAt: new Date(), PrivacyLevel: 0 } as Base)
    const [defaultTags, setDefaultTags] = useState<string[]>([])
    const [structure, setStructure] = useState<Structure | undefined>(undefined)

    const onFileSelect = (file: File) => {
        setFiles((prev) => [...prev, file])
    }

    return (
        <div>
            <Grid container spacing={2}>
                <Grid item xs={4}>
                    <FormControl fullWidth sx={{ my: 2 }}>
                        <StructureSelector
                            required
                            disabled={disabled}
                            onChange={(v, s) => {
                                setStructure(s)
                            }}
                            selectedStructure={(s) => {
                                if (!structure) {
                                    setStructure(s)
                                }

                                if (!structure?.ID) {
                                    setStructure(s)
                                }
                            }}
                            value={structure?.ID}
                            error={''}
                            allowedStructures={cdi?.Structures}
                        />
                    </FormControl>

                    <BaseForm
                        value={defaultBase}
                        onChange={setDefaultBase}
                        contentType={type === 'document' ? 'cm.document' : 'cm.image'}
                    />

                    <FormControl fullWidth sx={{ my: 1 }}>
                        <TagsSelector
                            selected={defaultTags || []}
                            disabled={disabled}
                            tagTypes={[type === 'document' ? TagType.Document : TagType.Image]}
                            onChange={setDefaultTags}
                            allowedTags={cdi?.Tags || []}
                            min={cdi?.TagsMinMax[0]}
                            max={cdi?.TagsMinMax[1]}
                        />
                    </FormControl>
                </Grid>
                <Grid item xs={8}>
                    <FileSelector type={type} multiple={multiple} onFileSelect={onFileSelect} sizeLimit={sizeLimit} />

                    {files.map((file) => (
                        <FileCard
                            key={file.name}
                            file={file}
                            type={type}
                            disabled={!!disabled}
                            onUpload={() => {}}
                            onDelete={() => {
                                setFiles(files.filter((f) => f.name !== file.name))
                            }}
                        />
                    ))}
                </Grid>
            </Grid>
        </div>
    )
}

type UploaderDialogProps = UploaderProps & {
    onClose: () => void
    open: boolean
}

export const UploaderDialog = ({ onClose, open, ...props }: UploaderDialogProps) => {
    const handleClose = (_e: unknown, reason: 'backdropClick' | 'escapeKeyDown') => {
        onClose()
    }

    return (
        <CMDialog open={open} onClose={handleClose} title='Upload' fullWidth maxWidth='lg'>
            <DialogContent>
                <Uploader {...props} />
            </DialogContent>
        </CMDialog>
    )
}

export const UploaderButton = ({ ...props }: UploaderProps) => {
    const [open, setOpen] = useState(false)

    return (
        <>
            <Button onClick={() => setOpen(true)}>Upload</Button>
            {open && <UploaderDialog {...props} open={open} onClose={() => setOpen(false)} />}
        </>
    )
}
