import { z } from 'zod'
import { entity, trackable } from '@/common/react-query'
import { base } from '@/pkgs/content/types'

export type FileType = z.infer<typeof fileSchema>

export const fileSchema = z.object({
    ...entity.shape,
    ...trackable.shape,

    Active: z.boolean(),
    Status: z.string(),

    StorageID: z.string(),
    Type: z.string(),
    ContentType: z.string(),
    FileSize: z.number(),
    Extension: z.string(),
    Hash: z.string(),
    ETag: z.string(),

    ...base.shape,
    Title: z.string(), // Alt for images
    Filename: z.string(),
    FolderID: z.string(),
    Description: z.string(),
    Keywords: z.array(z.string()).nullish(),

    Tags: z.array(z.string()).nullish(),
    Meta: z.record(z.string(), z.string()).nullish(),

    StructureID: z.string().nullish(),
    Data: z.any().nullish(),

    Width: z.number().nullish(),
    Height: z.number().nullish()
})
