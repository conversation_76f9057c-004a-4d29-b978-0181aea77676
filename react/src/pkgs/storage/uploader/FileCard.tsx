import { useEffect, useState } from 'react'
import { FileType } from '@/pkgs/storage/uploader/types'

type FileCardProps = {
    file: File
    disabled: boolean
    type: 'image' | 'document'
    onUpload: (file: File) => void
    onDelete: (file: File) => void
}

export const FileCard = ({ file, onUpload, onDelete, type, disabled }: FileCardProps) => {
    const [state, setState] = useState<FileType>()
    const [hash, setHash] = useState<string>('')

    useEffect(() => {
        calculateSHA256(file).then(setHash)
    }, [file])

    return (
        <div>
            <div>{file.name}</div>
            <div>
                {file.size} bytes / {file.type}
            </div>
            <div>SHA256: {hash || 'calculating...'}</div>
        </div>
    )
}

async function calculateSHA256(file: File): Promise<string> {
    const buffer = await file.arrayBuffer()

    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)

    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
}
