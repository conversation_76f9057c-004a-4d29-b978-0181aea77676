import { GridColDef } from '@mui/x-data-grid'
import { CellLine, CellWrapper, IDToNameCell, MenuLightCell, TwoLinesCell } from '@/pkgs/grid/cells/GridCells'

export function geQueriesGridColumns(menuItems: any): GridColDef[] {
    return [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 1.4,
            filterable: false,
            sortable: true,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'StructureID',
            headerName: 'Structure',
            flex: 1.5,
            filterable: false,
            sortable: false,
            renderCell: (params) => <IDToNameCell tableName={'structure'} ID={params.row.StructureID} />
        },
        {
            field: 'ContentTypes',
            headerName: 'Content Types',
            flex: 1,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.ContentTypes?.join(', ')}</CellLine>
                </CellWrapper>
            )
        },
        {
            field: 'Menu',
            headerName: '...',
            width: 80,
            sortable: false,
            filterable: false,
            renderCell: (params) => <MenuLightCell itemsFactory={menuItems(params.row)} />
        }
    ]
}
