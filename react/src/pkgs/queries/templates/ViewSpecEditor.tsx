import { FormControl<PERSON>abel, MenuItem, <PERSON>u<PERSON>ist, Stack, Switch } from '@mui/material'
import { <PERSON>E<PERSON>or, CodeEditorHandler } from '@/pkgs/monaco/code-editor'
import { Box } from '@mui/system'
import React, { useRef } from 'react'
import { <PERSON>ButtonV2, SplitButtonV2Handle } from '@/pkgs/queries/SplitButtonV2'
import { AddCircleOutline } from '@mui/icons-material'

type ViewSpecEditorProps = {
    value: string
    language: 'javascript' | 'html' | 'css'
    defaultValue: string
    onChange: (value: string | undefined) => void
    snippets?: Array<{ label: string; content: string }>
}

export const ViewSpecEditor = ({ value, language, defaultValue, onChange, snippets = [] }: ViewSpecEditorProps) => {
    const [isCustom, setIsCustom] = React.useState(!!value && value !== defaultValue && value !== '')
    const [editorValue, setEditorValue] = React.useState(value || '')
    const editorRef = useRef<CodeEditorHandler>(null)
    const splitButtonRef = useRef<SplitButtonV2Handle>(null)

    React.useEffect(() => {
        if (value !== editorValue) {
            setEditorValue(value || '')
        }
    }, [value])

    const handleCustomToggle = (checked: boolean) => {
        setIsCustom(checked)
        if (checked) {
            // When switching to custom, populate with default if empty
            const newValue = editorValue || defaultValue
            setEditorValue(newValue)
            onChange(newValue)
        } else {
            // When switching to default, clear the value
            setEditorValue('')
            onChange(undefined)
        }
    }

    const handleEditorChange = (newValue: string | undefined) => {
        setEditorValue(newValue || '')
        onChange(newValue)
    }

    // Disable switch if custom value exists and differs from default
    const isSwitchDisabled = isCustom && editorValue !== '' && editorValue !== defaultValue

    return (
        <Stack direction='column' spacing={1} sx={{ flex: 1, height: '100%' }}>
            <Stack direction={'row'} alignItems={'flex-end'}>
                <FormControlLabel
                    control={
                        <Switch
                            size={'small'}
                            checked={isCustom}
                            onChange={(e) => handleCustomToggle(e.target.checked)}
                            disabled={isSwitchDisabled}
                        />
                    }
                    label={'Use custom'}
                    labelPlacement={'start'}
                    title={isSwitchDisabled ? 'Clear editor content to use default' : undefined}
                />
                <div style={{ flex: 1 }} />
                <SplitButtonV2
                    ref={splitButtonRef}
                    label={'Insert'}
                    icon={<AddCircleOutline />}
                    size='small'
                    variant='contained'
                >
                    <MenuList id='split-button-menu' autoFocusItem>
                        {snippets.map((snippet, index) => (
                            <MenuItem
                                key={snippet.label}
                                onClick={() => {
                                    editorRef.current?.insertTextAtCursor(snippet.content)
                                    splitButtonRef.current?.close()
                                }}
                            >
                                {snippet.label}
                            </MenuItem>
                        ))}
                    </MenuList>
                </SplitButtonV2>
            </Stack>

            <Box sx={{ flex: 1, overflow: 'auto', display: 'flex', height: '100%' }}>
                <CodeEditor
                    ref={editorRef}
                    height={'100%'}
                    value={isCustom ? editorValue : defaultValue}
                    language={language}
                    onChange={handleEditorChange}
                    disabled={!isCustom}
                />
            </Box>
        </Stack>
    )
}
