import { describe, expect, it } from 'vitest'
import { defaultScopes, Scope } from './scopes'
import { EntityScopeEnum } from './entityScope'
import { matchExists, patternMatch, trimAction } from './permissions/patternMatch'

describe('check scopes: a scope pattern should exist for each contentType', () => {
    const matcher = (pattern: string, strToCheck: string) => patternMatch(trimAction(pattern), strToCheck)
    const patterns = getAllPatterns(defaultScopes)
    const contentTypesAvailable = Object.values(EntityScopeEnum)

    contentTypesAvailable.forEach((contentType) => {
        it(`${contentType}`, () => {
            const exists = matchExists(patterns, contentType, matcher)
            expect(exists, `${contentType} scope pattern should exist`).toBe(true)
        })
    })
})

function getAllPatterns(scopes: Scope[]): string[] {
    let patterns: string[] = []

    scopes.forEach((scope) => {
        patterns.push(scope.pattern)

        if (scope.children && scope.children.length > 0) {
            patterns = patterns.concat(getAllPatterns(scope.children))
        }
    })

    return patterns
}
