import { describe, expect, it } from 'vitest'
import { Account, Group, Role, ROLE_TYPE } from '@/pkgs/user-management/types'
import testCases from './field_restriction_data.json'
import { isFieldRestricted } from '@/pkgs/auth/permissions/field_restriction'
import { asSecured } from '@/pkgs/auth/permissions/securityMapping'

describe('field_restriction', () => {
    testCases.forEach((testCase: FieldRestrictionTestType) => {
        const account = getAccount(
            testCase.Authorizable.IsAdmin,
            testCase.Authorizable.Groups.map((group) => getGroup(group.SiteID, ...group.Scopes))
        )

        it(testCase.Description, () => {
            const secured = asSecured(testCase.Secured)
            const actionResult = isFieldRestricted(account, secured, testCase.Field)
            expect(actionResult).toBe(testCase.ShouldBeRestricted)
        })
    })
})

type FieldRestrictionTestType = {
    Description: string
    Secured: {
        Sites: string[] | null
        DepartmentID: string | null
        EntityScope: string
    }
    Authorizable: {
        IsAdmin: boolean
        Groups: {
            SiteID: string | null
            Scopes: string[]
        }[]
    }
    Field: string
    ShouldBeRestricted: boolean
}

function getAccount(isAdmin: boolean, groups: Group[]): Account {
    return {
        IsAdmin: isAdmin,
        Groups: groups,
        Email: '<EMAIL>',
        Firstname: 'firstname',
        Lastname: 'lastname',
        Active: true,
        ExternalGroups: [],
        ManualGroups: [],
        NativeGroups: [],
        GroupsWarnings: [],
        Settings: null,
        ID: '1',
        CreatedAt: new Date(),
        CreatedBy: 'createdBy',
        UpdatedAt: new Date(),
        UpdatedBy: 'updatedBy'
    }
}

function getGroup(siteID: string | null, ...scopes: string[]): Group {
    return {
        SiteID: siteID,
        Role: getRole(...scopes),

        Active: true,
        ID: '1',
        Name: 'name',
        Type: 'manual',
        Description: 'description',
        ExternalIdList: [],
        DependsOn: [],
        Audience: [],
        AvailableGroups: [],
        UpdatedBy: 'updatedBy',
        UpdatedAt: new Date(),
        CreatedBy: 'createdBy',
        CreatedAt: new Date(),
        RoleID: '1'
    }
}

function getRole(...scopes: string[]): Role {
    return {
        id: '1',
        name: 'name',
        mod_core: 0,
        mod_pages: 0,
        mod_news: 0,
        mod_events: 0,
        mod_media: 0,
        mod_transportation: 0,
        mod_alerts: 0,
        active: true,
        Scopes: [...scopes],
        type: ROLE_TYPE.TENANT
    }
}
