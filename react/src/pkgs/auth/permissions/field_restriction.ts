import { matchExists, patternMatch } from '@/pkgs/auth/permissions/patternMatch'
import { asSecured } from '@/pkgs/auth/permissions/securityMapping'
import { Identity, Secured } from '@/pkgs/auth/permissions/evaluate'
import { useAppContext } from '@/pkgs/auth/atoms'

const LOG = false

export function isFieldRestricted(account: Identity, secured: Secured, fieldName: string) {
    if (account.IsAdmin) return false

    if (!account.Groups || !Array.isArray(account.Groups) || !account.Groups.length) {
        LOG && console.warn(`User ${account.Email} has no groups, nothing is restricted`)
        return false
    }

    // cm.content.page -> cm.field_restriction:content.page
    const scope = secured.EntityScope.replace(/^cm\./, 'cm.field_restriction:') + '.' + fieldName

    // No support for tenant-wide entities (basically, it works only for content)
    let sites = secured.Sites || []

    if (secured.DepartmentID) {
        sites = [secured.DepartmentID]
    }

    for (const site of sites) {
        const accountScopesForSite: string[] = []

        // get all scopes for this site
        for (const group of account.Groups) {
            if (
                group.SiteID === null || // tenant-wide group
                group.SiteID === site // site-specific group
            ) {
                accountScopesForSite.push(...group.Role.Scopes)
            }
        }

        if (matchExists(accountScopesForSite, scope, patternMatch)) {
            return true
        }
    }

    return false
}

export function useFieldRestriction(obj: any) {
    const appContext = useAppContext()
    const identity = appContext.identity()
    const secured = asSecured(obj)

    return (fieldName: string) => (identity ? isFieldRestricted(identity, secured, fieldName) : true)
}
