import React, { Dispatch, useCallback, useMemo, useState } from 'react'
import DocumentGallery from '../media/document/DocumentGallery'
import getJsonDctSchema from '../../helpers/getJsonDctSchema'
import DctComponentList from './DctComponentList'
import AppAccordion from '../../common/components/AppAccordion'
import { Box, Typography } from '@mui/material'
import DisabledByDefaultIcon from '@mui/icons-material/DisabledByDefault'
import { colours } from '../../common/colours'
import 'react-toastify/dist/ReactToastify.css'
import { useGetLinkToDocument } from '../media/copyToClipboard'
import { mainBorderRadius } from '@/app/theme'

interface DctSectionProps {
    expanded?: boolean
    expandedOnChangeHandler?: (expanded: boolean) => void
    allExpandedOnChangeHandler?: (expanded: boolean) => void
    disabled: boolean
    isFieldRestricted: (field: string) => boolean
    section: any
    contentModelData: any
    contentModelDataOnChange: (value: any) => void
    contentModelDataErrors: Record<string, any>
    setContentModelDataErrors: Dispatch<Record<string, any>>
}

function getDefaultValueByComponentType(type: string) {
    if (type === 'rich-text') {
        return { html: '' }
    }
    if (type === 'image') {
        return { src: '', alt: '' }
    }
    return ''
}

export function DctSection({
    expanded,
    expandedOnChangeHandler,
    allExpandedOnChangeHandler,
    disabled,
    isFieldRestricted,
    section,
    contentModelData,
    contentModelDataOnChange,
    contentModelDataErrors,
    setContentModelDataErrors
}: DctSectionProps) {
    const getLinkToDocument = useGetLinkToDocument()
    const sectionComponents = getJsonDctSchema(section.components)

    const [isImageGalleryOpen, setIsImageGalleryOpen] = useState(false)
    const [currentComponent, setCurrentComponent] = useState<any | null>(null)
    const [isDocumentGalleryOpen, setIsDocumentGalleryOpen] = useState(false)
    const emptySectionData = useMemo(
        () =>
            sectionComponents.reduce(
                (a, component) => ({
                    ...a,
                    [component.name]: getDefaultValueByComponentType(component.type)
                }),
                {} as Record<string, any>
            ),
        [sectionComponents]
    )

    const updateContentModelData = useCallback(
        (field: string, value: any) => {
            contentModelDataOnChange({
                // exp
                // ...(contentModelData || {}),
                ...(contentModelData?.[section.name] || {}),
                [field]: value
            })
        },
        [contentModelData, section.name, contentModelDataOnChange]
    )

    const updateContentModelDataErrors = useCallback(
        (field: string, value: boolean) => {
            setContentModelDataErrors((p) => ({
                ...p,
                [section.name]: {
                    // exp
                    // ...contentModelDataErrors,
                    ...contentModelDataErrors?.[section.name],
                    [field]: value
                }
            }))
        },
        [contentModelDataErrors, section.name, setContentModelDataErrors]
    )

    // exp
    // const localState = Object.keys(contentModelData || {}).length
    //     ? contentModelData
    //     : emptySectionData
    const localState = Object.keys(contentModelData?.[section.name] || {}).length
        ? contentModelData?.[section.name]
        : emptySectionData

    const errorState = contentModelDataErrors?.[section.name]

    const insertDocumentIntoComponentState = (document, isFolder) => {
        if (document && document.id && currentComponent) {
            updateContentModelData(
                currentComponent.name,
                getLinkToDocument({ doc: document, fullUrl: false, by: 'id' })
            )
            setIsDocumentGalleryOpen(false)
        }
    }

    return (
        <>
            <AppAccordion
                expanded={expanded || false}
                summary={
                    <Typography
                        component='p'
                        variant='h5'
                        sx={
                            disabled
                                ? {
                                      color: colours.disabled,
                                      borderRadius: mainBorderRadius
                                  }
                                : {
                                      borderRadius: mainBorderRadius
                                  }
                        }
                    >
                        {section.title}
                        {disabled ? (
                            <DisabledByDefaultIcon sx={{ fontSize: '16px' }} titleAccess='Disabled' />
                        ) : undefined}
                    </Typography>
                }
                onChangeHandler={expandedOnChangeHandler}
                allOnChangeHandler={allExpandedOnChangeHandler}
                details={
                    <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                        {section.description && <p className='no-margin  mb10'>{section.description}</p>}
                        <DctComponentList
                            sectionName={section.name}
                            sectionComponents={sectionComponents}
                            disabled={disabled}
                            isFieldRestricted={isFieldRestricted}
                            localState={localState}
                            setLocalState={(field: string, value: any) => updateContentModelData(field, value)}
                            errorState={errorState}
                            setErrorState={(field: string, value: boolean) =>
                                updateContentModelDataErrors(field, value)
                            }
                            imageGalleryOpenHandler={setIsImageGalleryOpen}
                            currentComponentOnChangeHandler={setCurrentComponent}
                            documentGalleryOpenHandler={setIsDocumentGalleryOpen}
                        />
                    </Box>
                }
            />
            {/*
            {isImageGalleryOpen && !disabled && (
                <ImageGallery
                    open={isImageGalleryOpen}
                    close={() => setIsImageGalleryOpen(false)}
                    onChange={insertIntoCard}
                    allowedCropSizes={}
                />
            )} */}

            {isDocumentGalleryOpen && !disabled && (
                <DocumentGallery
                    isForDct
                    saveForDct={insertDocumentIntoComponentState}
                    isGalleryOpen={isDocumentGalleryOpen}
                    setGalleryClose={() => setIsDocumentGalleryOpen(false)}
                />
            )}
        </>
    )
}
