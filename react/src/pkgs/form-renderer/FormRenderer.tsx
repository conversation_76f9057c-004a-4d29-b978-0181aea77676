import { forwardRef, useCallback, useImperativeHandle, useState } from 'react'
import { FormStructure } from '../structure/types'
import _ from 'lodash'
import { DctSection } from './DctSection'
import { DctCollection } from './DctCollection'
import { Box, BoxProps } from '@mui/material'
import { emailRegex } from '@/common/constants'
import { defaultMetaAccordionId, defaultSEOAccordionId } from '../content/editor/ContentEditor'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

export function getAccordionSectionId(section, suffix) {
    return section.name
    // return section.title + section.name + suffix
}

// arrayOfSections = structure
export function getAccordionInitialState(
    arrayOfSections: Record<string, any>[],
    defaultIsOpen = true
): Record<string, boolean> {
    const initialValue = typeof defaultIsOpen === 'boolean' ? defaultIsOpen : true
    const isSectionExpandedMap =
        arrayOfSections?.reduce(
            (a, section, index) => ({
                ...a,
                [getAccordionSectionId(section, index)]: initialValue
            }),
            {}
        ) || {}

    return isSectionExpandedMap
}

interface FormRendererProps {
    value: any
    onChange: (value: any) => void
    formStructure: FormStructure[] | undefined
    isFieldRestricted: (field: string) => boolean

    // Used to control accordions that are not in form renderer
    accordionExpandedOnChange?: (id: string | string[], expanded: boolean) => void

    disabled?: boolean
    boxProps?: BoxProps
}

const FormRenderer = forwardRef<any, FormRendererProps>(
    ({ value, onChange, formStructure, accordionExpandedOnChange, disabled, boxProps, isFieldRestricted }, ref) => {
        boxProps = {
            sx: {
                ...boxProps?.sx,
                width: '100%'
            },
            ...boxProps
        }

        const _getAccordionInitialState = useCallback(
            (expanded?: boolean) => {
                return getAccordionInitialState(formStructure || [], expanded)
            },
            [formStructure]
        )

        const [formStructureDataErrors, setFormStructureDataErrors] = useState({})
        const [accordion, setAccordion] = useState<Record<string, boolean>>({
            ..._getAccordionInitialState()
        })

        const validateValue = () => {
            if (!formStructure) {
                return [null, null]
            }
            const invalidDctComponentNames: string[] = []
            const errorsObj = _.cloneDeep(value)
            for (const section of formStructure) {
                for (const component of section.components as any) {
                    if (!errorsObj[section.name]) {
                        errorsObj[section.name] = {}
                    }
                    const sectionValues = value?.[section.name]
                    if (Array.isArray(sectionValues)) {
                        // allowMultiple: true
                        // TBD
                    } else {
                        const sectionComponentValue = value?.[section.name]?.[component.name]

                        if (component?.type === 'email') {
                            // email
                            if (
                                (component?.required || !!sectionComponentValue) &&
                                !emailRegex.test(sectionComponentValue)
                            ) {
                                errorsObj[section.name][component.name] = true
                                invalidDctComponentNames.push(component.name)
                            } else {
                                errorsObj[section.name][component.name] = false
                            }
                        } else if (typeof sectionComponentValue === 'object') {
                            switch (component?.type) {
                                case 'query':
                                    if (component?.required && !sectionComponentValue?.queryID) {
                                        errorsObj[section.name][component.name] = true
                                        invalidDctComponentNames.push(component.name)
                                    } else {
                                        errorsObj[section.name][component.name] = false
                                    }
                                    break
                                default:
                                    // spotlight images
                                    if (
                                        component?.required &&
                                        sectionComponentValue.src &&
                                        !sectionComponentValue.src.length
                                    ) {
                                        errorsObj[section.name][component.name] = true
                                        invalidDctComponentNames.push(component.name)
                                    } else {
                                        errorsObj[section.name][component.name] = false
                                    }
                            }
                        } else if (
                            (component?.required && !sectionComponentValue) ||
                            (component?.maximumLength && sectionComponentValue?.length > component?.maximumLength)
                        ) {
                            errorsObj[section.name][component.name] = true
                            invalidDctComponentNames.push(component.name)
                        } else {
                            errorsObj[section.name][component.name] = false
                        }
                    }
                }
            }
            setFormStructureDataErrors(errorsObj)
            const invalidSections: string[] = []
            Object.keys(errorsObj).forEach((section) => {
                if (errorsObj[section] && Object.values(errorsObj[section]).some((v) => v === true)) {
                    invalidSections.push(section)
                }
            })
            if (invalidSections.length) {
                const shouldExpand = {}
                invalidSections.forEach((section: string) => {
                    shouldExpand[section] = true
                })
                setAccordion({ ...accordion, ...shouldExpand })
            }
            return [invalidDctComponentNames, errorsObj]
        }

        useImperativeHandle(ref, () => {
            return {
                validate() {
                    const [invalidDctComponentNames, errorsObj] = validateValue()
                    return invalidDctComponentNames
                },
                validateAndReturnErrors() {
                    const [invalidDctComponentNames, errorsObj] = validateValue()
                    return [invalidDctComponentNames, errorsObj]
                },
                setAccordion(id: string, expanded: boolean) {
                    setAccordion({ ...accordion, [id]: expanded })
                },
                processServerError(err: any) {
                    const serverError = guessErrorMessage(err)
                    try {
                        const updatedErrors = _.cloneDeep(formStructureDataErrors)
                        const sectionsToExpand = {}

                        const errorItems = serverError
                            .split(';')
                            .map((item) => item.trim())
                            .filter(Boolean)

                        errorItems.forEach((errorItem) => {
                            // Try standard format with pipe
                            const parts = errorItem.split('|')
                            if (parts.length === 2) {
                                const [, location] = parts
                                const locationParts = location.split('.')

                                if (locationParts.length === 2) {
                                    const [sectionName, componentName] = locationParts
                                    if (sectionName && componentName) {
                                        if (!updatedErrors[sectionName]) updatedErrors[sectionName] = {}
                                        updatedErrors[sectionName][componentName] = true
                                        sectionsToExpand[sectionName] = true
                                    }
                                }
                            }
                        })

                        // Only update state if we found errors to set
                        if (Object.keys(updatedErrors).length > 0) {
                            setFormStructureDataErrors(updatedErrors)

                            // Expand sections with errors
                            if (Object.keys(sectionsToExpand).length > 0) {
                                setAccordion((prev) => ({ ...prev, ...sectionsToExpand }))
                            }
                        }
                    } catch (error) {
                        console.warn('Error processing server error:', error)
                    }
                },
                setAllAccordions(expanded: boolean) {
                    setAccordion(_getAccordionInitialState(expanded))
                }
            }
        })

        if (!formStructure) {
            return <></>
        }

        return (
            <Box {...boxProps} data-testid='form-renderer'>
                {formStructure.map((section, index) => {
                    const accordionSectionId = getAccordionSectionId(section, index)
                    return section.allowMultiple ? (
                        // error handling within DctCollection
                        // error only shown for current item being edited on add/update since it is a list of items
                        // will prevent adding to list if error
                        <DctCollection
                            key={section.name + section.title + JSON.stringify(section)}
                            section={section}
                            contentModelData={value}
                            contentModelDataOnChange={(updatedValue) => {
                                onChange({
                                    ...value,
                                    [section.name]: updatedValue
                                })
                            }}
                            isFieldRestricted={isFieldRestricted}
                            expanded={accordion[accordionSectionId]}
                            expandedOnChangeHandler={(expanded) => {
                                setAccordion({ ...accordion, [accordionSectionId]: expanded })
                            }}
                            allExpandedOnChangeHandler={
                                formStructure.length > 1 || !!accordionExpandedOnChange
                                    ? (expanded) => {
                                          setAccordion(_getAccordionInitialState(expanded))
                                          accordionExpandedOnChange?.(
                                              [defaultSEOAccordionId, defaultMetaAccordionId],
                                              expanded
                                          )
                                      }
                                    : undefined
                            }
                            disabled={disabled || false}
                        />
                    ) : (
                        <DctSection
                            key={section.name + section.title + JSON.stringify(section)}
                            section={section}
                            // contentModelData={value?.[section.name]}
                            contentModelData={value}
                            contentModelDataOnChange={(updatedValue) => {
                                onChange({
                                    ...value,
                                    [section.name]: updatedValue
                                })
                            }}
                            isFieldRestricted={isFieldRestricted}
                            contentModelDataErrors={formStructureDataErrors}
                            setContentModelDataErrors={setFormStructureDataErrors}
                            expanded={accordion[accordionSectionId]}
                            expandedOnChangeHandler={(expanded) => {
                                setAccordion({ ...accordion, [accordionSectionId]: expanded })
                            }}
                            allExpandedOnChangeHandler={
                                formStructure.length > 1 || !!accordionExpandedOnChange
                                    ? (expanded) => {
                                          setAccordion(_getAccordionInitialState(expanded))
                                          accordionExpandedOnChange?.(
                                              [defaultSEOAccordionId, defaultMetaAccordionId],
                                              expanded
                                          )
                                      }
                                    : undefined
                            }
                            disabled={disabled || false}
                        />
                    )
                })}
            </Box>
        )
    }
)

export default FormRenderer
