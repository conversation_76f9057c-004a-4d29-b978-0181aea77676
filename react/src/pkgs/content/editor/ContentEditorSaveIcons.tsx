import { useCallback } from 'react'
import { Box, Tooltip } from '@mui/material'
import CheckIcon from '@mui/icons-material/Check'
import SaveIcon from '@mui/icons-material/Save'
import { getTimeSince } from '@/helpers/getTimeSince'
import CustomIconButton from '@/common/components/CustomIconButton'
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'
import { AutoSaveSwitch } from './auto-save/AutoSaveSwitch'

export function ContentEditorSaveIcons({
    hasChanges,
    unsavedChangesIconOnClickHandler,
    queryStatus,
    lastUpdated,
    hideSwitch
}: {
    hasChanges: boolean
    unsavedChangesIconOnClickHandler: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void
    queryStatus: 'error' | 'loading' | 'success'
    lastUpdated: Date
    hideSwitch?: boolean
}) {
    const IconToShow = useCallback(() => {
        if (hasChanges) {
            return (
                <Tooltip title={queryStatus == 'error' ? '' : 'You have unsaved changes'}>
                    <CustomIconButton
                        color={queryStatus == 'error' ? 'error' : 'warning'}
                        onClick={unsavedChangesIconOnClickHandler}
                    >
                        <ErrorOutlineIcon />
                    </CustomIconButton>
                </Tooltip>
            )
        }

        if (queryStatus == 'loading') {
            return (
                <Tooltip title={'Saving'} sx={{ padding: '8px' }}>
                    <SaveIcon color={'info'} />
                </Tooltip>
            )
        }

        return (
            <Tooltip title={`Editor is up to date. Last updated: ${getTimeSince(lastUpdated)}`} sx={{ padding: '8px' }}>
                <CheckIcon color='success' />
            </Tooltip>
        )
    }, [hasChanges, queryStatus, lastUpdated])

    return (
        <Box sx={{ display: 'flex' }}>
            <Box sx={{ display: 'flex', marginLeft: '.4rem', alignItems: 'center' }}>
                <Box
                    sx={{
                        position: 'relative',
                        marginRight: '.4rem',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                >
                    <IconToShow />
                </Box>
                {hideSwitch == false && <AutoSaveSwitch />}
            </Box>
        </Box>
    )
}
