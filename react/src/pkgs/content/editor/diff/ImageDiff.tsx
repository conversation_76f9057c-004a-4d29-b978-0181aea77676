import { colours } from '@/common/colours'
import { getImagePreviewUrl } from '@/pkgs/storage/getImagePreviewUrl'
import { Box, Grid, Typography } from '@mui/material'

function NoImage() {
    return (
        <Box
            sx={{
                height: '100%',
                width: `200px`,
                backgroundColor: colours.off_white_but_darker,
                justifyContent: 'center',
                alignItems: 'center',
                display: 'flex'
            }}
            title='No image'
        >
            <Typography variant='caption'>No image</Typography>
        </Box>
    )
}
export function ImageDiff({ beforeImageId, afterImageId }: { beforeImageId: string; afterImageId: string }) {
    const isEqual = beforeImageId == afterImageId
    return (
        <Grid
            container
            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', width: 'calc(100% - 30px)' }}
        >
            <Grid
                item
                md={6}
                height='100%'
                padding={'12px'}
                sx={{ backgroundColor: isEqual ? undefined : colours.monaco_diff_before }}
            >
                {!!afterImageId ? (
                    <img
                        src={getImagePreviewUrl(afterImageId || '')}
                        width='200px'
                        alt={afterImageId}
                        title={beforeImageId}
                    />
                ) : (
                    <NoImage />
                )}
            </Grid>
            <Grid
                item
                md={6}
                height='100%'
                padding={'12px'}
                sx={{ backgroundColor: isEqual ? undefined : colours.monaco_diff_after }}
            >
                {!!beforeImageId ? (
                    <img
                        src={getImagePreviewUrl(beforeImageId || '')}
                        width='200px'
                        alt={beforeImageId}
                        title={beforeImageId}
                    />
                ) : (
                    <NoImage />
                )}
            </Grid>
        </Grid>
    )
}
