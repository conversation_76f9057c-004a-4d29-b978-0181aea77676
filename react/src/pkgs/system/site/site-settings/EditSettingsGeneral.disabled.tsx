// import {
//     fireEvent,
//     getByDisplayValue,
//     getByLabelText,
//     prettyDOM,
//     queryByDisplayValue,
//     queryByText,
//     render
// } from '@testing-library/react'
// import { globalWrapper } from './EditSiteSettings.test'
// import EditSettingsGeneral, { fieldsConfig } from './EditSettingsGeneral'
// import * as useSiteForAccountsQueryMutation from '../../services/query-hooks/useSiteForAccountsQueryMutation'
// import { SiteType } from '../../conf'
//
// const mockQueryHookData = {
//     data: {
//         name: 'Mock Site Name',
//         description: 'Mock Site Description',
//         type: Object.values(SiteType)[0],
//         tags: [],
//         settings: {
//             slogan: 'This is a slogan',
//             address: '1358th Street',
//             city: 'Vancouver',
//             province: 'BC',
//             postal: '1A1A1A',
//             email: '<EMAIL>',
//             phone: '************',
//             fax: '************',
//             Primary: '#f90000', // red
//             Secondary: undefined,
//             LogoSrc: 'test img src'
//         }
//     },
//     isLoading: false,
//     error: {},
//     mutation: jest.fn() as any
// }
//
// describe('EditSettingsGeneral.tsx', () => {
//     beforeEach(() => {
//         jest.spyOn(useSiteForAccountsQueryMutation, 'default').mockImplementation(() => ({
//             siteData: mockQueryHookData.data,
//             error: mockQueryHookData.error,
//             refetch: jest.fn(),
//             mutation: mockQueryHookData.mutation
//         }))
//     })
//
//     test('all fields are visible', () => {
//         const screen = render(<EditSettingsGeneral />, { wrapper: globalWrapper })
//
//         // on load, values are visible
//         Object.keys(fieldsConfig).forEach((fieldKey) => {
//             // if required, incurs new line whitespace
//             const fieldConfig = fieldsConfig[fieldKey]
//             let labelText = fieldConfig?.label
//             if (fieldConfig.required) {
//                 labelText = labelText + ' *'
//             }
//
//             const l1 = screen.queryByLabelText(labelText, { exact: false })
//             const l2 = screen.queryByTestId(`${fieldConfig.label}-label`)
//             console.log('fieldKey', fieldKey)
//             expect(l1 || l2).toBeTruthy()
//
//             if (fieldConfig?.helperText) {
//                 expect(screen.getByText(fieldConfig.helperText!)).toBeTruthy()
//             }
//         })
//     })
//
//     test('fields are prefilled', () => {
//         const { queryByText, getByDisplayValue } = render(<EditSettingsGeneral />, { wrapper: globalWrapper })
//
//         function getByTextOrDisplayValue(value: string) {
//             return queryByText(value) || getByDisplayValue(value)
//         }
//
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.name)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.description)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.type)).toBeVisible()
//
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.slogan)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.address)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.city)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.province)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.postal)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.email)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.phone)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.fax)).toBeVisible()
//         expect(getByTextOrDisplayValue(mockQueryHookData.data.settings.Primary)).toBeVisible()
//     })
//
//     test('image', async () => {
//         const screen = render(<EditSettingsGeneral />, { wrapper: globalWrapper })
//
//         const imgForm = screen.queryByTestId(`${fieldsConfig['LogoSrc'].label}-form`)
//         if (imgForm) console.log(prettyDOM(imgForm))
//         const imgElement = imgForm?.querySelector('img')
//         expect(imgElement).toHaveAttribute('src', mockQueryHookData.data.settings.LogoSrc)
//
//         const deleteButton = screen.getByText('Delete').querySelector('span')
//
//         if (deleteButton) {
//             expect(deleteButton).toBeVisible()
//             fireEvent.click(deleteButton)
//         } else {
//             fail('delete button not visible')
//         }
//
//         expect(screen.queryByText('Delete')).toBeNull()
//         expect(imgForm?.querySelector('img')).toBeNull()
//
//         const addAnImageButton = screen.getByText('Add an image')
//         expect(addAnImageButton).toBeVisible()
//
//         fireEvent.click(addAnImageButton)
//
//         expect(screen.getByText('Image Gallery')).toBeInTheDocument()
//         expect(screen.getByText('Images')).toBeInTheDocument()
//         expect(screen.getByText('Upload images')).toBeInTheDocument()
//     })
//
//     test('action buttons', () => {
//         const { getByText, queryByDisplayValue, getByLabelText } = render(<EditSettingsGeneral />, {
//             wrapper: globalWrapper
//         })
//
//         // save is initially disabled
//         const saveButton = getByText('Save')
//         expect(saveButton).toBeDisabled()
//
//         const newEmail = '<EMAIL>'
//         fireEvent.change(getByLabelText(fieldsConfig['email'].label, { exact: false }), { target: { value: newEmail } })
//
//         expect(queryByDisplayValue(newEmail)).toBeTruthy()
//
//         expect(saveButton).not.toBeDisabled()
//
//         fireEvent.click(getByText('Reset Changes'))
//
//         expect(saveButton).toBeDisabled()
//
//         expect(queryByDisplayValue(newEmail)).toBeNull()
//     })
//
//     // TBD:
//     // test('validators', () => {
//
//     // })
// })
