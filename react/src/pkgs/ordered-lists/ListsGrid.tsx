import { ListsQuery } from './types'
import { useStateWithStorage } from '@/common/storage.service'
import { useDebounce } from '@/common/useDebounce'
import { useListsQuery } from './queries'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { Button, FormControlLabel, Grid, Switch } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { StructureSelector } from '../structure/StructureSelector'
import { ListForm } from './ListForm'
import { AddButton } from '../../common/components'
import PageContainerWithHeader from '../../common/components/PageContainerWithHeader'
import SearchBar from '../../common/components/SearchBar'
import { StatusSelector } from '../content/BaseForm'
import { sitesEventChanel } from '@/common/components/selectors/SiteSelectorForAccount'
import { defaultPageQuery } from '@/common/react-query'
import { DataGridBase } from '../grid/DataGridBase'
import { TagType } from '../system/tags/types'
import { getListGridColumns } from '@/pkgs/ordered-lists/listsGridColumns'
import { listsGridMenu } from '@/pkgs/ordered-lists/listsGridMenu'
import { useAppContext } from '@/pkgs/auth/atoms'
import { EntityScopeEnum } from '@/pkgs/auth/entityScope'
import { TagsSelector } from '../system/tags/TagsSelector'

const defaultQuery: ListsQuery = { ...defaultPageQuery }
export const ListsGrid = () => {
    const appContext = useAppContext()

    const [query, setQuery] = useState(defaultQuery)
    const debouncedQuery = useDebounce(query, 300)
    const results = useListsQuery(debouncedQuery)

    const { menuItems, selectedList, editAs, listEditorIsOpen, setListEditorIsOpen, setSelectedList } = listsGridMenu(
        () => results.refetch()
    )

    useEffect(() => {
        const subs = sitesEventChanel.on('onSiteUpdated', (siteID) => {
            // Test: refetch() here has a race condition: siteID is correct, however, if you go from one site to the next, it refetches
            // with the previous value.
            // we don't have siteID as a param for lists (using r.Request.SiteID), so this is not properly fixed, but it does work as expected.
            //@ts-ignore
            setQuery((p) => ({ ...p, SiteID: siteID }))
        })
        return () => subs()
    }, [])

    useEffect(() => {
        // Because the list editor does not close on save/update we must pass in the list with the updated values into ListForm
        // in order to ensure hasChanges within ListForm is reset to false.
        if (selectedList) {
            const upToDateSelectedList = results.data?.Rows?.find((list) => list.ID == selectedList?.ID)
            if (upToDateSelectedList) {
                setSelectedList(upToDateSelectedList as any)
            }
        }
    }, [results])

    const columns = getListGridColumns(menuItems)

    return (
        <PageContainerWithHeader
            title={'Lists'}
            topRightElement={
                <AddButton
                    title={'Add List'}
                    func={() => {
                        setSelectedList(undefined)
                        setListEditorIsOpen(true)
                    }}
                />
            }
        >
            <Grid container spacing={2}>
                {/* FILTERS */}
                <Grid item xs={4}>
                    <SearchBar
                        value={query.Search || ''}
                        onChange={(val) => {
                            setQuery((prev) => ({ ...prev, Search: val || '', page: 1 }))
                        }}
                    />
                </Grid>
                <Grid item xs={4}>
                    <StructureSelector
                        value={query.StructureID}
                        onChange={(v) => setQuery({ ...query, StructureID: v, page: 1 })}
                    />
                </Grid>
                <Grid item xs={4}>
                    <TagsSelector
                        selected={query.Tags || []}
                        onChange={(tags) => setQuery((p) => ({ ...query, Tags: tags, page: 1 }))}
                        tagTypes={[TagType.List]}
                    />
                </Grid>
                <Grid item xs={4}>
                    <StatusSelector value={query.Status} onChange={(v) => setQuery({ ...query, Status: v, page: 1 })} />
                </Grid>
                <Grid item xs={2}>
                    <FormControlLabel
                        value='start'
                        control={
                            <Switch
                                checked={query.SiteOnly || false}
                                onChange={(e) =>
                                    setQuery({ ...query, SiteOnly: e.target.checked, Status: '', page: 1 })
                                }
                                color='secondary'
                            />
                        }
                        label='Site Only'
                        labelPlacement='start'
                    />
                </Grid>
                <Grid item xs={2}>
                    <FormControlLabel
                        value='start'
                        control={
                            <Switch
                                checked={query.Inactive || false}
                                onChange={(e) =>
                                    setQuery({ ...query, Inactive: e.target.checked, Status: '', page: 1 })
                                }
                                color='secondary'
                            />
                        }
                        label='Deleted'
                        labelPlacement='start'
                    />
                </Grid>
                <Grid item xs={4}>
                    <Button
                        style={{ marginTop: '0.25rem', float: 'right' }}
                        onClick={() => setQuery(defaultQuery)}
                        color={'primary'}
                    >
                        Reset Filters
                    </Button>
                </Grid>
                {/* GRID */}
                <Grid item xs={12}>
                    {results.isLoading && <div>Loading...</div>}
                    {results.error && <div>Error: {guessErrorMessage(results.error)}</div>}
                    {results.data && (
                        <DataGridBase
                            filterMode={'client'}
                            getRowClassName={(params) =>
                                appContext.action(
                                    {
                                        Sites: params.row['Sites'],
                                        DepartmentID: params.row['DepartmentID'],
                                        EntityScope: EntityScopeEnum.List
                                    },
                                    'update'
                                )
                                    ? ''
                                    : 'disabled-row'
                            }
                            // rowHeight={100}
                            columns={columns}
                            state={results.data}
                            setQuery={setQuery}
                        />
                    )}
                </Grid>
            </Grid>

            {listEditorIsOpen && (
                <ListForm
                    value={selectedList}
                    open={listEditorIsOpen}
                    onClose={() => {
                        setListEditorIsOpen(false)
                        setSelectedList(undefined)
                    }}
                    onSave={() => {
                        results.refetch()
                    }}
                    editAs={editAs}
                />
            )}
        </PageContainerWithHeader>
    )
}
