<!DOCTYPE html>
<html>
    <head>
        <title>{{Subject}}</title>
        <script src="https://www.google.com/recaptcha/api.js?render={{PUBLIC_KEY}}"></script>
        <script>
            function addResourceToHead(url) {
                const head = document.head;

                // Extract the file extension from the URL before any query parameters
                const extension = url.split('?')[0].split('.').pop().toLowerCase();

                // Check the file extension to decide if it's a script or a stylesheet
                if (extension === 'js') {
                    const scriptElement = document.createElement('script');
                    scriptElement.src = url;
                    scriptElement.type = 'text/javascript';
                    head.appendChild(scriptElement);
                    console.log('Script added:', url);
                } else if (extension === 'css') {
                    const linkElement = document.createElement('link');
                    linkElement.href = url;
                    linkElement.rel = 'stylesheet';
                    linkElement.type = 'text/css';
                    head.appendChild(linkElement);
                    console.log('Stylesheet added:', url);
                } else {
                    console.error('Unsupported file type:', url);
                }
            }

            async function sys_validate(func) {
                // Return true if the function is not provided
                if (typeof func !== 'function') {
                    return true;
                }

                try {
                    // Attempt to call the function
                    const result = await Promise.resolve(func());

                    // Return true as the function executed without throwing an error
                    return true;
                } catch (error) {
                    // Return false if an error is thrown
                    return false;
                }
            }
        </script>
     </head>

    <body>

        {{{Form}}}

        <script>
            (async () => {
                document.querySelectorAll('form').forEach(function(form) {
                    form.addEventListener('submit', async function(event) {
                      event.preventDefault();
                      const isValid = await sys_validate(window['beforeSubmit']);
                      if (!isValid) {
                        return;
                      }

                      grecaptcha.ready(function() {
                        grecaptcha.execute('{{PUBLIC_KEY}}', {action: 'submit'}).then(function(token) {
                          var hiddenInput = document.createElement('input');
                          hiddenInput.type = 'hidden';
                          hiddenInput.name = 'recaptcha_token';
                          hiddenInput.value = token;
                          form.appendChild(hiddenInput);
                          console.log(token);

                          // Submit the form now that we have appended the token
                          form.submit();
                        });
                      });
                    });
                  });
            })();
        </script>
    </body>
</html>
