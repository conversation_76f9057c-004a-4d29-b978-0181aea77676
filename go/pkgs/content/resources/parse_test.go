package resources

import (
	"contentmanager/tests/test_utils"
	"path/filepath"
	"strings"
	"testing"
)

func Test_Parse(t *testing.T) {
	paths, err := filepath.Glob(filepath.Join("testdata", "*.resources"))
	if err != nil {
		t.Fatal(err)
	}

	for _, path := range paths {
		t.Run(path, func(t *testing.T) {
			content := test_utils.MustReadFile(strings.Replace(path, ".resources", "", 1))
			expectedResources := test_utils.MustReadLines(path)

			resources := Extract(content)

			compareResources(t, expectedResources, resources)
		})
	}
}

func compareResources(t *testing.T, expected, actual []string) {
	t.Helper()

	expectedMap := make(map[string]bool)
	actualMap := make(map[string]bool)

	for _, e := range expected {
		expectedMap[e] = true
	}
	for _, a := range actual {
		actualMap[a] = true
	}

	// Check for items in expected but missing from actual
	for e := range expectedMap {
		if !actualMap[e] {
			t.<PERSON><PERSON>("missing resource in actual: %s", e)
		}
	}

	// Check for extra items in actual that weren't expected
	for a := range actualMap {
		if !expectedMap[a] {
			t.E<PERSON>rf("unexpected resource in actual: %s", a)
		}
	}
}
