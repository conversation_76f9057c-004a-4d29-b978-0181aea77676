package public

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/common"
	uuid "github.com/satori/go.uuid"
)

func GetDistributedPageContent(r *shared.AppContext, parentID uuid.UUID, siteID uuid.UUID) string {
	var c content.Content
	if err := common.DistributedPageQuery(r.TenantDatabase(), parentID, siteID).First(&c).Error; err != nil {
		r.<PERSON>gger().Error().Err(err).Msgf("Failed to get distributed page content for page/site: %s/%s", parentID.String(), siteID.String())
		return ""
	}
	return c.Content
}
