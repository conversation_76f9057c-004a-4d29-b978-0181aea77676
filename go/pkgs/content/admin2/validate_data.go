package admin2

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/structure"
	"errors"
	"fmt"
	"strings"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func ValidateData(db *gorm.DB, c content.Content, structureID uuid.UUID) error {
	if c.PublishAt == nil {
		return nil
	}

	var s structure.Structure
	if err := db.Where("id = ?", structureID).First(&s).Error; err != nil {
		return err
	}

	sections, err := s.Sections()
	if err != nil {
		return err
	}

	for _, section := range sections {
		if section.AllowMultiple {
			continue
		}

		sectionName := section.Name
		for _, component := range section.Components {
			if len(component.UniqScope) == 0 {
				continue
			}

			componentName := component.Name
			path := fmt.Sprintf("%s.%s", sectionName, componentName)

			switch component.Type {
			// one-line strings
			case "text", "email", "select", "date", "contact-form-link":
				var val string
				if err := jsonxx.UnmarshalJSONAtPath(c.Data, path, &val); err != nil {
					if !component.Required && err.Error() == "[UnmarshalJSONAtPath] value not found at path" {
						continue
					}

					return err
				}
				if component.Required && len(val) == 0 {
					return fmt.Errorf("component %s is required", path)
				}

				// skip empty values for optional components
				if len(val) == 0 {
					continue
				}

				var cc []content.Content
				tx := db.Where("active").Where("id != ?", c.ID).
					Where("structure_id = ?", structureID).
					Where("publish_at is not null")

				if component.UniqScope == "site" {
					tx = tx.Where(pgxx.ArrayHasAny("sites", c.Sites))
				}

				if err := tx.Where(fmt.Sprintf("data->'%s'->>'%s' = ?", sectionName, componentName), val).Find(&cc).Error; err != nil {
					return err
				}
				if len(cc) == 0 {
					continue
				}

				sb := &strings.Builder{}
				sb.WriteString(fmt.Sprintf("Component value is not unique in scope `%s` (Section: %s, Component: %s). Conflicting content: ", component.UniqScope, section.Title, component.Label))
				for _, c := range cc {
					sb.WriteString(fmt.Sprintf("\n - %s (%s), ", c.Title, c.ID.String()))
				}
				sb.WriteString(" |")
				sb.WriteString(path)

				return errors.New(sb.String())

			default:
				continue
			}
		}
	}

	return nil
}
