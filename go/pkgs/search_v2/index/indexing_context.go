package index

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/pkgs/service_context"
	"context"
	"errors"
	"gorm.io/gorm"
)

type IndexingContext struct {
	Tenant        multitenancy.Tenant
	TenantDB      *gorm.DB
	Sites         []models.Site
	TenancyConfig config.AppConfig
	Context       context.Context
	MTA           multitenancy.Accessor
}

func ContextFromAppContext(r *shared.AppContext) (*IndexingContext, error) {
	sites, err := r.Sites()
	if err != nil {
		return nil, err
	}

	tenancyServiceConfig := config.Init()
	tenantDB := r.TenantDatabaseUnscoped()
	if tenantDB == nil {
		return nil, errors.New("tenant database not found")
	}

	return &IndexingContext{
		Tenant:        r.Tenant(),
		TenantDB:      tenantDB,
		Sites:         sites,
		TenancyConfig: *tenancyServiceConfig,
		Context:       context.Background(),
		MTA:           r.MultitenancyAccessor().WithContext(context.Background()),
	}, nil
}

func ContextFromServiceContextForTenant(serviceCtx service_context.ServiceContext, tenant multitenancy.Tenant) (*IndexingContext, error) {
	tenancyDB := serviceCtx.TenancyDB()
	if tenancyDB == nil {
		return nil, errors.New("[ContextFromServiceContextForTenant] tenancy database not found")
	}

	tenantDB := serviceCtx.TenantDB(tenant.ID)
	if tenantDB == nil {
		return nil, errors.New("[ContextFromServiceContextForTenant] tenant database not found")
	}

	var sites []models.Site
	if err := tenancyDB.Where(" tenant_id = ?", tenant.ID).
		Where(" active = true ").
		Preload("Hosts", "active = true").
		//Preload("Departments", "active = true").
		Find(&sites).
		Error; err != nil {
		serviceCtx.Logger().Warn().Err(err).Str("tenant", tenant.Name).Msg("Failed to get sites")
		return nil, err
	}

	indexingContext := &IndexingContext{
		Tenant: multitenancy.Tenant{
			Entity: commonModels.Entity{
				ID: tenant.ID,
			},
			Name:     tenant.Name,
			Settings: tenant.Settings,
			// Don't need them for services. Site/SiteId don't exist in a service.
			//Host:       "",
			//Server:     "",
			//Dbuser:     "",
			//Dbpassword: "",
			//SiteId:     uuid.NullUUID{},
			//Site:       tenancyModels.Site{},
		},
		TenantDB:      tenantDB,
		Sites:         sites,
		TenancyConfig: serviceCtx.TenancyConfig(),
		Context:       serviceCtx.Context(),
		MTA:           serviceCtx.MultitenancyAccessor(),
	}

	return indexingContext, nil
}
