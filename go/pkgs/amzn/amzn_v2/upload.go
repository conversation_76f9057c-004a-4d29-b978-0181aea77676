package amzn_v2

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/config"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

var s3Client *s3.Client
var presignClient *s3.PresignClient

func init() {
	// Load the AWS configuration (e.g., credentials, region)
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion("ca-central-1"))
	if err != nil {
		panic(fmt.Errorf("failed to load AWS configuration: %w", err))
	}

	// Create an S3 client
	s3Client = s3.NewFromConfig(cfg)
	presignClient = s3.NewPresignClient(s3Client)
}

type (
	// PostPolicyOutput includes **ordered** fields for consistent signature generation
	PostPolicyOutput struct {
		URL       string            `json:"url"`
		Fields    []PostPolicyField `json:"fields"` // Using array instead of map to preserve order
		ExpiresAt time.Time         `json:"expiresAt"`
	}

	PostPolicyField struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	PolicyProps struct {
		BucketName  string
		Key         string
		Expiration  time.Duration
		MaxFileSize int64
		ContentType string
	}
)

// GeneratePresignedPostPolicy generates a signed POST policy for browser uploads
func GeneratePresignedPostPolicy(ctx context.Context, props PolicyProps) (*PostPolicyOutput, error) {
	presignResult, err := presignClient.PresignPostObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(props.BucketName),
		Key:    aws.String(props.Key),
	}, func(opts *s3.PresignPostOptions) {
		opts.Expires = props.Expiration

		// Define conditions for the policy
		conditions := []interface{}{
			// Content type restriction
			[]interface{}{"eq", "$Content-Type", props.ContentType},
			[]interface{}{"content-length-range", 1, props.MaxFileSize},
		}
		opts.Conditions = conditions
	})
	if err != nil {
		return nil, fmt.Errorf("failed to presign POST request: %w", err)
	}

	orderedFields := []PostPolicyField{}

	// Define the order of fields that AWS expects
	fieldOrder := []string{
		"key",
		"bucket",
		"X-Amz-Algorithm",
		"X-Amz-Credential",
		"X-Amz-Date",
		"X-Amz-Security-Token", // if present
		"Policy",
		"X-Amz-Signature",
	}

	// Add fields in the specified order
	for _, fieldName := range fieldOrder {
		if value, exists := presignResult.Values[fieldName]; exists {
			orderedFields = append(orderedFields, PostPolicyField{
				Key:   fieldName,
				Value: value,
			})
		}
	}

	// Add any remaining fields that weren't in our predefined order
	for key, value := range presignResult.Values {
		// Check if we already added this field
		alreadyAdded := false
		for _, field := range orderedFields {
			if field.Key == key {
				alreadyAdded = true
				break
			}
		}
		if !alreadyAdded {
			orderedFields = append(orderedFields, PostPolicyField{
				Key:   key,
				Value: value,
			})
		}
	}

	output := &PostPolicyOutput{
		URL:       presignResult.URL,
		Fields:    orderedFields,
		ExpiresAt: time.Now().Add(props.Expiration),
	}

	return output, nil
}
