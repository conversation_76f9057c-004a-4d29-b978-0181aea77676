package idmap

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	models2 "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	models3 "contentmanager/pkgs/notifications/models"
	"contentmanager/pkgs/notifications/shared"
	"contentmanager/pkgs/structure"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"sync"
)

type IDToNamesQuery map[string][]uuid.UUID

type IDMapResponse map[string]map[string]string

func GetIDMap(r *shared.AppContext, q IDToNamesQuery) result.Result[IDMapResponse] {
	var res IDMapResponse = make(map[string]map[string]string, len(q))
	var mu sync.Mutex

	var wg sync.WaitGroup
	for table, uuids := range q {
		wg.Add(1)
		go func(t string, ii []uuid.UUID) {
			defer wg.Done()

			tableMap := getFieldsValuesByIDs(r, t, ii, "")
			mu.Lock()
			defer mu.Unlock()
			res[t] = tableMap
		}(table, uuids)
	}
	wg.Wait()

	return result.Success(res)
}

type M struct {
	Select shared_notifications.IMappable
	Search shared_notifications.ISearchable
}

var modelsMap = map[string]M{
	// multitenancy
	"site": {Select: models2.Site{}, Search: nil},

	// tenant (notifications)
	"account":    {Select: identity.Account{}, Search: nil},
	"subscriber": {Select: models3.Subscriber{}, Search: models3.Subscriber{}},
	"topic":      {Select: models3.Topic{}, Search: nil},
	"schedule":   {Select: models3.Schedule{}, Search: nil},
	"bus_route":  {Select: commonModels.BusRoute{}, Search: nil},
	"issue":      {Select: models3.Issue{}, Search: models3.Issue{}},
	"relay":      {Select: models3.Relay{}, Search: nil},
	"role":       {Select: commonModels.Role{}, Search: nil},
	"structure":  {Select: structure.Structure{}, Search: nil},
}

func getFieldsValuesByIDs(r *shared.AppContext, table string, ids []uuid.UUID, search string) map[string]string {
	res := map[string]string{}
	t, ok := modelsMap[table]
	if !ok || (len(ids) == 0 && len(search) == 0) {
		logging.FromContext(r.Request().Context()).Error().Msgf("[GetIDMap]: no tables for query: %s, %+v", table, ids)
		return res
	}
	var db *gorm.DB
	switch table {
	case "site":
		db = r.TenancyDB()
	default:
		db = r.TenantDatabase()
	}
	db = db.Table(table).Select("id", t.Select.SelectNameQuery())

	if len(ids) > 0 {
		db = db.Where("id in ? ", ids)
	}
	if t.Search != nil && len(search) > 0 {
		db = db.Where(t.Search.SearchQuery(), "%"+search+"%").Limit(100)
	}

	var results []map[string]interface{}
	if err := db.Find(&results).Error; err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("[GetIDMap] Can't query DB. ")
		return res
	}
	if len(results) == 0 {
		logging.FromContext(r.Request().Context()).Error().Msgf("[GetIDMap] Nothing found in %v for %v. ", table, ids)
		return res
	}

	for _, m := range results {
		id, idOk := m["id"].(string)
		name, nameOk := m["name"].(string)
		if !idOk || !nameOk {
			continue
		}
		res[id] = name
	}
	return res
}
