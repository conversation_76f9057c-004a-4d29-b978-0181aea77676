package search

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	publicControllers "contentmanager/library/tenant/public/controllers"
	"contentmanager/library/utils"
	"net/http"
)

func AddPublicRoutes(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Get("/search", GetSearchPage)
	r.Get("/sys/c/api/search", GetSearchResults)
	return r
}

func GetSearchPage(w http.ResponseWriter, r *shared.AppContext) {
	var params = NewSearchParamFromRequest(r)
	var data, err = ISearchAdapter().GetAndCompileSearchResults(r, params)
	if err != nil {
		data = err.Error()
	}
	publicControllers.WriteWithStandardHeaders(w, r, []byte(data))
}
func GetSearchResults(w http.ResponseWriter, r *shared.AppContext) {
	var params = NewSearchParamFromRequest(r)
	var data, err = ISearchAdapter().GetSearchResults(r, params)
	if err != nil {
		utils.WriteResponseJSON(w, result.Error(err, []SearchResultsViewModel{}), err)
		return
	}
	utils.WriteResponseJSON(w, result.Success[[]SearchResultsViewModel](data), nil)
	return
}
