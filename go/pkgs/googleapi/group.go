package googleapi

import (
	b64 "encoding/base64"
	"golang.org/x/net/context"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/admin/directory/v1"
	"google.golang.org/api/option"
)

type Group struct{}

func (g *Group) GetAllGroups(authStr string, adminUser string) (*admin.Groups, error) {
	return g.GetAllGroupsPaged(authStr, adminUser, "")
}

func (g *Group) GetAllGroupsPaged(authStr string, adminUser string, pageToken string) (*admin.Groups, error) {
	return g.GetAllGroupsPagedWithKeyFilter(authStr, adminUser, "", pageToken)
}

func (g *Group) GetAllGroupsPagedWithKeyFilter(authStr string, adminUser string, groupKey string, pageToken string) (*admin.Groups, error) {
	ctx := context.Background()
	return g.GetAllGroupsPagedWithKeyFilterWithCancellationContext(ctx, authStr, adminUser, groupKey, pageToken)
}

func (g *Group) GetAllGroupsPagedWithKeyFilterWithCancellationContext(ctx context.Context, authStr string, adminUser string, groupKey string, pageToken string) (*admin.Groups, error) {
	applicationIdentity, err := b64.StdEncoding.DecodeString(authStr)

	if err != nil {
		return &admin.Groups{}, err
	}

	config, err := google.JWTConfigFromJSON(applicationIdentity,
		admin.AdminDirectoryUserReadonlyScope,
		admin.AdminDirectoryGroupReadonlyScope,
		admin.AdminDirectoryGroupMemberReadonlyScope,
	)

	if err != nil {
		return &admin.Groups{}, err
	}
	config.Subject = adminUser
	tokenSource := config.TokenSource(ctx)
	userService, err := admin.NewService(ctx, option.WithTokenSource(tokenSource))

	if err != nil {
		return &admin.Groups{}, err
	}
	// Copy "NextPageToken"  into PageToken(string) Received after first request. if no "NextPageToken" received from google then there is no Next page
	req, err := userService.Groups.List().UserKey(groupKey).PageToken(pageToken).Do()
	return req, err
}

func (g *Group) GetUserGroups(authStr string, adminUser string, accountUser string) (*admin.Groups, error) {
	return g.GetUserGroupsPaged(authStr, adminUser, accountUser, "")
}

func (g *Group) GetUserGroupsPaged(authStr string, adminUser string, accountUser string, pageToken string) (*admin.Groups, error) {
	ctx := context.Background()
	return g.GetUserGroupsPagedWithCancellationContext(&ctx, authStr, adminUser, accountUser, pageToken)
}

func (g *Group) GetUserGroupsPagedWithCancellationContext(ctx *context.Context, authStr string, adminUser string, accountUser string, pageToken string) (*admin.Groups, error) {
	applicationIdentity, err := b64.StdEncoding.DecodeString(authStr)

	if err != nil {
		return &admin.Groups{}, err
	}

	if ctx == nil {
		type ct struct {
			Val *context.Context
		}
		c := context.Background()
		ctx = ct{Val: &c}.Val
	}

	config, err := google.JWTConfigFromJSON(applicationIdentity,
		admin.AdminDirectoryUserReadonlyScope,
		admin.AdminDirectoryGroupReadonlyScope,
		admin.AdminDirectoryGroupMemberReadonlyScope,
	)

	if err != nil {
		return &admin.Groups{}, err
	}
	config.Subject = adminUser
	tokenSource := config.TokenSource(*ctx)
	userService, err := admin.NewService(*ctx, option.WithTokenSource(tokenSource))

	if err != nil {
		return &admin.Groups{}, err
	}

	// accountUser is email of User whose Group membership are retreived.
	req, err := userService.Groups.List().UserKey(accountUser).PageToken(pageToken).Do()
	return req, err
}

func (g *Group) GetGroupsMembers(authStr string, adminUser string, groupKey string) (*admin.Members, error) {
	return g.GetGroupsMembersPaged(authStr, adminUser, groupKey, "")
}

func (g *Group) GetGroupsMembersPaged(authStr string, adminUser string, groupKey string, pageToken string) (*admin.Members, error) {
	ctx := context.Background()
	return g.GetGroupsMembersPagesWithCancellationContext(&ctx, authStr, adminUser, groupKey, pageToken)
}

func (g *Group) GetGroupsMembersPagesWithCancellationContext(ctx *context.Context, authStr string, adminUser string, groupKey string, pageToken string) (*admin.Members, error) {
	applicationIdentity, err := b64.StdEncoding.DecodeString(authStr)

	if err != nil {
		return &admin.Members{}, err
	}

	if ctx == nil {
		type ct struct {
			Val *context.Context
		}
		c := context.Background()
		ctx = ct{Val: &c}.Val
	}

	config, err := google.JWTConfigFromJSON(applicationIdentity,
		admin.AdminDirectoryUserReadonlyScope,
		admin.AdminDirectoryGroupReadonlyScope,
		admin.AdminDirectoryGroupMemberReadonlyScope,
	)

	if err != nil {
		return &admin.Members{}, err
	}
	config.Subject = adminUser
	tokenSource := config.TokenSource(*ctx)
	userService, err := admin.NewService(*ctx, option.WithTokenSource(tokenSource))

	if err != nil {
		return &admin.Members{}, err
	}

	// groupKey is email of User whose Group membership are retreived.
	req, err := userService.Members.List(groupKey).PageToken(pageToken).Do()

	return req, err
}

func (g *Group) GetGroupsMembersTransitive(authStr string, adminUser string, groupKey string) (*admin.Members, error) {
	return g.GetGroupsMembersTransitivePaged(authStr, adminUser, groupKey, "")
}

func (g *Group) GetGroupsMembersTransitivePaged(authStr string, adminUser string, groupKey string, pageToken string) (*admin.Members, error) {
	ctx := context.Background()
	return g.GetGroupsMembersTransitivePagedWithCancellationContext(ctx, authStr, adminUser, groupKey, pageToken)
}

func (g *Group) GetGroupsMembersTransitivePagedWithCancellationContext(ctx context.Context, authStr string, adminUser string, groupKey string, pageToken string) (*admin.Members, error) {
	applicationIdentity, err := b64.StdEncoding.DecodeString(authStr)

	if err != nil {
		return &admin.Members{}, err
	}

	config, err := google.JWTConfigFromJSON(applicationIdentity,
		admin.AdminDirectoryUserReadonlyScope,
		admin.AdminDirectoryGroupReadonlyScope,
		admin.AdminDirectoryGroupMemberReadonlyScope,
	)

	if err != nil {
		return &admin.Members{}, err
	}
	config.Subject = adminUser
	tokenSource := config.TokenSource(ctx)
	userService, err := admin.NewService(ctx, option.WithTokenSource(tokenSource))

	if err != nil {
		return &admin.Members{}, err
	}

	// groupKey is email of User whose Group membership are retreived.
	req, err := userService.Members.List(groupKey).IncludeDerivedMembership(true).PageToken(pageToken).Do()

	return req, err
}
