package googleapi

import (
	b64 "encoding/base64"
	"golang.org/x/net/context"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/admin/directory/v1"
	"google.golang.org/api/option"
)

type OrgUnit struct{}

func (o *OrgUnit) GetAllOrgUnits(authStr string, customerId string, adminUser string) (*admin.OrgUnits, error) {
	applicationIdentity, err := b64.StdEncoding.DecodeString(authStr)

	if err != nil {
		return &admin.OrgUnits{}, err
	}

	ctx := context.Background()

	config, err := google.JWTConfigFromJSON(applicationIdentity,
		admin.AdminDirectoryUserReadonlyScope,
		admin.AdminDirectoryCustomerReadonlyScope,
		admin.AdminDirectoryOrgunitReadonlyScope,
	)

	if err != nil {
		return &admin.OrgUnits{}, err
	}
	config.Subject = adminUser
	tokenSource := config.TokenSource(ctx)
	userService, err := admin.NewService(ctx, option.WithTokenSource(tokenSource))

	if err != nil {
		return &admin.OrgUnits{}, err
	}

	req, err := userService.Orgunits.List(customerId).Type("all").Do()
	return req, err
}
