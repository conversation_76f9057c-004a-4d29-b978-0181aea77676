package sorting

import (
	"contentmanager/library/utils/converters"
	"reflect"
	"testing"
	"time"
)

type Simple struct {
	A int
	B string
}

func TestSortWithMultipleCriteria(t *testing.T) {
	items := []Simple{
		{A: 1, B: "b"},
		{A: 1, B: "a"},
		{A: 0, B: "c"},
	}

	sortings := []Sorting{
		{FieldName: "A", Direction: "asc"}, // primary sort on field A
		{FieldName: "B", Direction: "asc"}, // secondary sort on field B
	}

	sorted := Sort(items, sortings)
	expected := []Simple{
		{A: 0, B: "c"}, // smallest A comes first
		{A: 1, B: "a"}, // then among A == 1, "a" comes before "b"
		{A: 1, B: "b"},
	}

	if !reflect.DeepEqual(sorted, expected) {
		t.<PERSON>rrorf("Expected sorted order %v, got %v", expected, sorted)
	}
}

type PersonPtr struct {
	Name    string
	Age     *int
	Active  bool
	Created interface{}
}

func TestSort_SingleFieldPtr(t *testing.T) {
	now := time.Now()
	earlier := now.Add(-24 * time.Hour)
	earlierMore := now.Add(-25 * time.Hour)

	people := []PersonPtr{
		{Name: "Bob", Age: converters.AsPointer(30), Active: true, Created: &now},
		{Name: "Alice", Age: converters.AsPointer(25), Active: false, Created: &earlierMore},
		{Name: "Charlie", Age: converters.AsPointer(35), Active: true, Created: &earlier},
	}

	tests := []struct {
		name     string
		sortings []Sorting
		want     []string // Expected names in order
	}{
		{
			name: "Sort by age ascending",
			sortings: []Sorting{
				{FieldName: "Age", Direction: "asc"},
			},
			want: []string{"Alice", "Bob", "Charlie"},
		},
		{
			name: "Sort by created time",
			sortings: []Sorting{
				{FieldName: "Created", Direction: "asc"},
			},
			want: []string{"Alice", "Charlie", "Bob"}, // earlier times first
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Sort(people, tt.sortings)

			// Extract names to check order
			var names []string
			for _, p := range result {
				names = append(names, p.Name)
			}

			if !reflect.DeepEqual(names, tt.want) {
				t.Errorf("Expected %v, got %v", tt.want, names)
			}
		})
	}
}

func TestSort_EmptySlice(t *testing.T) {
	// Test with empty slice
	var items []string
	sortings := []Sorting{
		{FieldName: "any", Direction: "asc"},
	}

	result := Sort(items, sortings)
	if len(result) != 0 {
		t.Errorf("Expected empty slice, got %v", result)
	}
}

func TestSort_EmptySortings(t *testing.T) {
	// Test with empty sortings
	items := []string{"b", "a", "c"}
	var sortings []Sorting

	result := Sort(items, sortings)

	// Should return the slice unchanged
	if !reflect.DeepEqual(result, items) {
		t.Errorf("Expected %v, got %v", items, result)
	}
}

type Person struct {
	Name    string
	Age     int
	Active  bool
	Created time.Time
	Address struct {
		City    string
		Country string
	}
}

func TestSort_SingleField(t *testing.T) {
	now := time.Now()
	earlier := now.Add(-24 * time.Hour)
	earlierMore := now.Add(-25 * time.Hour)

	people := []Person{
		{Name: "Bob", Age: 30, Active: true, Created: now},
		{Name: "Alice", Age: 25, Active: false, Created: earlierMore},
		{Name: "Charlie", Age: 35, Active: true, Created: earlier},
	}

	tests := []struct {
		name     string
		sortings []Sorting
		want     []string // Expected names in order
	}{
		{
			name: "Sort by name ascending",
			sortings: []Sorting{
				{FieldName: "Name", Direction: "asc"},
			},
			want: []string{"Alice", "Bob", "Charlie"},
		},
		{
			name: "Sort by name descending",
			sortings: []Sorting{
				{FieldName: "Name", Direction: "desc"},
			},
			want: []string{"Charlie", "Bob", "Alice"},
		},
		{
			name: "Sort by age ascending",
			sortings: []Sorting{
				{FieldName: "Age", Direction: "asc"},
			},
			want: []string{"Alice", "Bob", "Charlie"},
		},
		{
			name: "Sort by age descending",
			sortings: []Sorting{
				{FieldName: "Age", Direction: "desc"},
			},
			want: []string{"Charlie", "Bob", "Alice"},
		},
		{
			name: "Sort by created time",
			sortings: []Sorting{
				{FieldName: "Created", Direction: "asc"},
			},
			want: []string{"Alice", "Charlie", "Bob"}, // earlier times first
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Sort(people, tt.sortings)

			// Extract names to check order
			var names []string
			for _, p := range result {
				names = append(names, p.Name)
			}

			if !reflect.DeepEqual(names, tt.want) {
				t.Errorf("Expected %v, got %v", tt.want, names)
			}
		})
	}
}

func TestSort_MultipleFields(t *testing.T) {
	people := []Person{
		{Name: "Alice", Age: 30, Active: true},
		{Name: "Bob", Age: 25, Active: false},
		{Name: "Charlie", Age: 30, Active: true},
		{Name: "Diana", Age: 25, Active: true},
		{Name: "Eve", Age: 30, Active: false},
	}

	tests := []struct {
		name     string
		sortings []Sorting
		want     []string // Expected names in order
	}{
		{
			name: "Sort by age ascending, then name ascending",
			sortings: []Sorting{
				{FieldName: "Age", Direction: "asc"},
				{FieldName: "Name", Direction: "asc"},
			},
			want: []string{"Bob", "Diana", "Alice", "Charlie", "Eve"},
		},
		{
			name: "Sort by age descending, then name descending",
			sortings: []Sorting{
				{FieldName: "Age", Direction: "desc"},
				{FieldName: "Name", Direction: "desc"},
			},
			want: []string{"Eve", "Charlie", "Alice", "Diana", "Bob"},
		},
		{
			name: "Sort by active descending, then age ascending, then name descending",
			sortings: []Sorting{
				{FieldName: "Active", Direction: "desc"},
				{FieldName: "Age", Direction: "asc"},
				{FieldName: "Name", Direction: "desc"},
			},
			want: []string{"Diana", "Charlie", "Alice", "Bob", "Eve"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Sort(people, tt.sortings)

			// Extract names to check order
			var names []string
			for _, p := range result {
				names = append(names, p.Name)
			}

			if !reflect.DeepEqual(names, tt.want) {
				t.Errorf("Expected %v, got %v", tt.want, names)
			}
		})
	}
}

func TestSort_NestedFields(t *testing.T) {
	people := []Person{
		{
			Name: "Alice",
			Address: struct {
				City    string
				Country string
			}{
				City:    "Alice",
				Country: "USA",
			},
		},
		{
			Name: "Bob",
			Address: struct {
				City    string
				Country string
			}{
				City:    "Boston",
				Country: "USA",
			},
		},
		{
			Name: "Charlie",
			Address: struct {
				City    string
				Country string
			}{
				City:    "Charlie",
				Country: "UK",
			},
		},
		{
			Name: "Diana",
			Address: struct {
				City    string
				Country string
			}{
				City:    "Diana",
				Country: "France",
			},
		},
	}

	tests := []struct {
		name     string
		sortings []Sorting
		want     []string // Expected names in order
	}{
		{
			name: "Sort by address.country ascending",
			sortings: []Sorting{
				{FieldName: "Address.Country", Direction: "asc"},
				{FieldName: "Name", Direction: "asc"},
			},
			want: []string{"Diana", "Charlie", "Alice", "Bob"},
		},
		{
			name: "Sort by address.city descending",
			sortings: []Sorting{
				{FieldName: "Address.City", Direction: "desc"},
				{FieldName: "Name", Direction: "asc"},
			},
			want: []string{"Diana", "Charlie", "Bob", "Alice"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Sort(people, tt.sortings)

			// Extract names to check order
			var names []string
			for _, p := range result {
				names = append(names, p.Name)
			}

			if !reflect.DeepEqual(names, tt.want) {
				t.Errorf("Expected %v, got %v", tt.want, names)
			}
		})
	}
}

func TestSort_InvalidField(t *testing.T) {
	people := []Person{
		{Name: "Alice", Age: 30},
		{Name: "Bob", Age: 25},
		{Name: "Charlie", Age: 35},
	}

	// Sort by a non-existent field
	sortings := []Sorting{
		{FieldName: "NonExistentField", Direction: "asc"},
		{FieldName: "Name", Direction: "asc"}, // Should fall back to this
	}

	result := Sort(people, sortings)

	// Should still sort by Name as fallback
	expected := []string{"Alice", "Bob", "Charlie"}
	var names []string
	for _, p := range result {
		names = append(names, p.Name)
	}

	if !reflect.DeepEqual(names, expected) {
		t.Errorf("Expected %v, got %v", expected, names)
	}
}

func TestSort_InvalidDirection(t *testing.T) {
	people := []Person{
		{Name: "Alice", Age: 30},
		{Name: "Bob", Age: 25},
		{Name: "Charlie", Age: 35},
	}

	// Sort with invalid direction
	sortings := []Sorting{
		{FieldName: "Name", Direction: "invalid"},
	}

	result := Sort(people, sortings)

	// Should default to ascending
	expected := []string{"Alice", "Bob", "Charlie"}
	var names []string
	for _, p := range result {
		names = append(names, p.Name)
	}

	if !reflect.DeepEqual(names, expected) {
		t.Errorf("Expected %v, got %v", expected, names)
	}
}

type ComplexStruct struct {
	MapField     map[string]int
	SliceField   []string
	PointerField *string
}

func TestSort_UnsupportedTypes(t *testing.T) {
	str := "test"
	items := []ComplexStruct{
		{
			MapField:     map[string]int{"a": 1},
			SliceField:   []string{"b", "c"},
			PointerField: &str,
		},
		{
			MapField:     map[string]int{"b": 2},
			SliceField:   []string{"a", "b"},
			PointerField: nil,
		},
	}

	// These fields cannot be compared directly
	sortings := []Sorting{
		{FieldName: "MapField", Direction: "asc"},
		{FieldName: "SliceField", Direction: "asc"},
	}

	// Function should not panic
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Function panicked with: %v", r)
		}
	}()

	// Sort should ignore uncommparable fields
	_ = Sort(items, sortings)
}

func TestSort_NilPointers(t *testing.T) {
	str1 := "Alice"
	str2 := "Bob"

	type Person struct {
		Name *string
		Age  int
	}

	people := []Person{
		{Name: &str1, Age: 30},
		{Name: nil, Age: 25},
		{Name: &str2, Age: 35},
	}

	sortings := []Sorting{
		{FieldName: "Name", Direction: "asc"},
	}

	result := Sort(people, sortings)

	// Nil should come first in ascending order
	if result[0].Name != nil {
		t.Errorf("Expected nil to be first in sorting")
	}

	// Then other values should be in correct order
	if *result[1].Name != "Alice" || *result[2].Name != "Bob" {
		t.Errorf("Expected order: nil, Alice, Bob, got: %v, %v, %v",
			result[0].Name,
			result[1],
			result[2])
	}
}

// For performance testing
func BenchmarkSort(b *testing.B) {
	// Create a large dataset
	const size = 1000
	people := make([]Person, size)

	for i := 0; i < size; i++ {
		people[i] = Person{
			Name:   string(rune('A' + i%26)),
			Age:    i % 100,
			Active: i%2 == 0,
		}
	}

	sortings := []Sorting{
		{FieldName: "Age", Direction: "asc"},
		{FieldName: "Name", Direction: "desc"},
		{FieldName: "Active", Direction: "asc"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Sort(people, sortings)
	}
}
