package templates

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/queries/models"
	"errors"
	"github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetTemplateByID(r *shared.AppContext, id *uuid.UUID) models.Template {
	if id == nil {
		id = &tableDefaultID
	}

	var template models.Template

	if err := r.TenantDatabase().Where("id = ?", id).First(&template).Error; err != nil {
		template = GetDefaultTemplate()

		if errors.Is(err, gorm.ErrRecordNotFound) && *id == tableDefaultID {
			// Seed the default template if it doesn't exist
			if e := r.TenantDatabase().Create(&template); e != nil {
				r.<PERSON>().Error().Msgf("[GetTemplateByID] Failed to create default template: %v", e)
			}
		} else {
			r.<PERSON>gger().Error().Msgf("[GetTemplateByID] Failed to get template: %v", err)
		}
		return template
	}

	if err := ApplyDefaultTemplateValues(&template); err != nil {
		r.<PERSON>gger().Error().Msgf("[GetTemplateByID] Failed to apply default template values: %v", err)
	}

	return template
}

func ApplyDefaultTemplateValues(template *models.Template) error {
	defaultValues, ok := DefaultViewSpecs[template.Type]
	if !ok {
		return errors.New("[ApplyDefaultTemplateValues] unknown template type")
	}

	if len(template.WrapperTemplate) == 0 {
		template.WrapperTemplate = defaultValues.WrapperTemplate
	}
	if len(template.GroupTemplate) == 0 {
		template.GroupTemplate = defaultValues.GroupTemplate
	}
	if len(template.ItemTemplate) == 0 {
		template.ItemTemplate = defaultValues.ItemTemplate
	}
	if len(template.CSS) == 0 {
		template.CSS = defaultValues.CSS
	}
	if len(template.JS) == 0 {
		template.JS = defaultValues.JS
	}

	return nil
}
