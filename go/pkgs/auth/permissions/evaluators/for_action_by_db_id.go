package evaluators

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/permissions"
	"errors"
	"fmt"
	"github.com/satori/go.uuid"
)

func ForActionByID[S auth.SecuredDB](r *shared.AppContext, dbModel S, id uuid.UUID, action string) error {
	// Check permissions for DB model
	if err := r.TenantDatabase().First(dbModel, "id = ?", id).Error; err != nil {
		return err
	}
	if err := permissions.Evaluate(r.Account(), dbModel, action); err != nil {
		return err
	}
	return nil
}

func ForActionByDBIDs[S auth.SecuredDB](r *shared.AppContext, dbModels *[]S, ids []uuid.UUID, action string) error {
	// Check permissions for DB model only
	if err := r.TenantDatabase().Find(dbModels, "id in ?", ids).Error; err != nil {
		return err
	}
	models := converters.FromPointer(dbModels)
	foundIDs := slicexx.Select(models, func(content S) uuid.UUID {
		id, ok := getID(content)
		if !ok {
			return uuid.Nil
		}
		return id
	})
	if slicexx.Any(ids, func(id uuid.UUID) bool {
		return !slicexx.Contains(foundIDs, id)
	}) {
		return errors.New(fmt.Sprintf("not all ids found: %v", ids))
	}

	for _, content := range models {
		if err := permissions.Evaluate(r.Account(), content, action); err != nil {
			return err
		}
	}

	return nil
}
