package permissions

import (
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/policy"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
)

var validActions = map[string]interface{}{
	"create":         struct{}{},
	"update":         struct{}{},
	"delete":         struct{}{},
	"assign_primary": struct{}{},
	// "manage": struct{}{},
}

func evaluate(account auth.Authorizable, secured auth.Sharable, scope string, matcher func(string, string) bool) error {
	if account.IsTenantAdmin() {
		return nil
	}

	if len(secured.GetSites()) == 0 {
		//
		// Evaluate tenant-wide permissions
		//
		for _, group := range account.GetGroups() {
			if group.GetSiteID() != nil {
				continue
			}
			for _, accountScope := range group.GetScopes() {
				if matcher(accountScope, scope) {
					return nil
				}
			}
		}

		return auth.NewForbiddenError("No permissions for the tenant-wide action: " + scope)
	} else {
		//
		// Evaluate site-specific permissions
		//

		sites := secured.GetSites()
		if secured.GetDepartmentID() != nil {
			sites = []uuid.UUID{*secured.GetDepartmentID()}
		}
		var ee []uuid.UUID
		for _, siteID := range sites {
			accountScopes := []string{}
			for _, group := range account.GetGroups() {
				// nil -- tenant-wide permissions
				if group.GetSiteID() == nil || *group.GetSiteID() == siteID {
					accountScopes = append(accountScopes, group.GetScopes()...)
				}
			}

			if !matchExists(accountScopes, scope, matcher) {
				ee = append(ee, siteID)
			}
		}
		if len(ee) > 0 {
			return auth.NewForbiddenError(fmt.Sprintf("No permissions for the action %s for sites: %v", scope, ee))
		}
		return nil
	}
}

// Evaluate checks if the account has the correct permissions to perform an action on a content.
//
//		 IMPORTANT: The function won't check the correctness of the Sites array for the content.
//	   - If the account is an admin, it will always succeed.
//	   - If the content is a department content, it will check if the account has the correct permissions for the department.
//	     (so for department we ignore the Sites array and "inherit" the permissions from the department)
//	   - For the regular content, it will check if the account has the correct permissions for all the sites in the Sites array.
func Evaluate(account auth.Authorizable, secured auth.Secured, action string) error {
	if _, ok := validActions[action]; !ok {
		return errors.New("Invalid action: " + action)
	}
	// a scope consists of an entity (a noun) and an action (a verb), e.g.:
	// cm.content.page/create
	// - cm.content.page - is an entity scope
	// - create - is a scope action
	scope := secured.GetScopeEntity() + "/" + action

	return evaluate(account, secured, scope, policy.PatternMatch)
}

func EvaluateEntityScopeOnly(account auth.Authorizable, secured auth.Secured) error {
	if account.IsTenantAdmin() {
		return nil
	}

	scope := secured.GetScopeEntity()

	return evaluate(account, secured, scope, func(accountScope, scope string) bool {
		return policy.PatternMatch(trimAction(accountScope), scope)
	})
}

// GetSitesForScopes returns the list of site IDs for which the account has ANY of the specified permissions (scopes).
func GetSitesForScopes(account auth.Authorizable, scopes ...string) ([]uuid.UUID, error) {
	if account.IsTenantAdmin() {
		return nil, nil
	}

	var siteIDs []uuid.UUID
	for _, scope := range scopes {
		for _, group := range account.GetGroups() {
			for _, accountScope := range group.GetScopes() {
				if policy.PatternMatch(trimAction(accountScope), scope) {
					if group.GetSiteID() == nil {
						return nil, nil
					}
					siteIDs = append(siteIDs, *group.GetSiteID())
					break
				}
			}
		}
	}
	if len(siteIDs) == 0 {
		return nil, auth.NewForbiddenError("No permissions for the action(s): " + strings.Join(scopes, ", "))
	}
	return siteIDs, nil

}

func trimAction(pattern string) string {
	parts := strings.Split(pattern, "/")
	return parts[0]
}

func matchExists(accountScopes []string, scope string, matcher func(string, string) bool) bool {
	for _, accountScope := range accountScopes {
		if matcher(accountScope, scope) {
			return true
		}
	}
	return false
}
