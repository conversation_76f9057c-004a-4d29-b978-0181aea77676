package social

import (
	"contentmanager/etc/conf"
	"contentmanager/library/shared"
	"contentmanager/pkgs/auth/claims"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login"
	"contentmanager/pkgs/auth/login/google"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"context"
	"github.com/markbates/goth"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"time"
)

func AuthenticateSocialLogin(r *shared.AppContext, res http.ResponseWriter, user goth.User, manager token.TokenManager[identity.PublicAccount]) error {
	appConfig := config.GetAppConfig()

	// TODO: finalize thoughts on 'redirectURL' | 'redirectTo' from state and ensure that cookie is getting the right Domain.
	if user.Provider == "google" {
		redirectTo, err := login.GetStateURL(r.Request().Form.Get("state"))
		if err != nil {
			return err
		}
		jwt, err := google.Authenticate(r, appConfig.GCloudIdentityCertificate, user.Email, manager)
		if err != nil {
			// Log error and continue to the "parent" flow
			r.Logger().Warn().Err(err).Msg("[AuthenticateSocialLogin] Error getting google user info, probably not a district user. ")
		} else {
			claims.SetCookies(res, jwt, redirectTo)
			return nil
		}
	}

	encoded, err := manager.CreateToken(context.Background(), identity.PublicAccount{
		ID:           uuid.NewV4(),
		Name:         user.Name,
		Email:        user.Email,
		PrivacyLevel: 1,
	})

	if err != nil {
		return err
	}
	cookie := &http.Cookie{
		Name:     appConfig.CookieName,
		Value:    encoded,
		Domain:   r.Request().Host,
		Path:     "/",
		Secure:   true,
		HttpOnly: true,
		SameSite: http.SameSiteNoneMode,
		Expires:  time.Now().Add(conf.SessionCookieExpiry),
	}
	http.SetCookie(res, cookie)

	return nil
}
