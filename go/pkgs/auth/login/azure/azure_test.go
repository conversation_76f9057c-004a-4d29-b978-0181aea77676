package azure

import (
	"contentmanager/pkgs/msgraph"
	"testing"
)

/*
CBE tenant ID: ef161387-255a-4777-af36-80b8fce4ee73
*/
func Test_GetUserInfo(t *testing.T) {
	t.<PERSON><PERSON>()
	graphClient, errMSGraph := msgraph.NewGraphClient(
		"ef161387-255a-4777-af36-80b8fce4ee73",
		"7fd5e018-ae0d-488f-afc0-22f57589dba8", // appConfig.MSOAuthApplicationID,
		"",                                     // appConfig.MSOAuthClientSecret,
		false,
	)
	if errMSGraph != nil {
		t.Error("Error creating graph client")
		return
	}

	user, errU := graphClient.GetUser("<EMAIL>")
	if errU != nil {
		t.Error("Error getting user info")
		return
	}

	groups, err := user.GetTransitiveUserGroups()
	if err != nil {
		t.Error("Error getting user groups")
		return
	}

	t.Log(user)
	t.Log(groups)
}
