package identity

import (
	driver2 "contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared/errx"
	"contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
)

type GroupType string

const (
	EXTERNAL GroupType = "external"
	MANUAL   GroupType = "manual"
)

type SecurityGroup struct {
	ID uuid.UUID `gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	commonModels.Tracking
	Name            string                `gorm:"column:name;type:character varying(256)"`
	Description     string                `gorm:"column:description;type:TEXT"`
	RoleID          uuid.UUID             `gorm:"column:role_id;type:uuid;NOT NULL"`
	SiteID          *uuid.UUID            `gorm:"column:site_id;type:uuid"`
	Active          bool                  `gorm:"column:active;type:bool;NOT NULL;"`
	Role            commonModels.Role     `gorm:"foreignKey:role_id"`
	Type            GroupType             `gorm:"column:type;"`
	ExternalIdList  driver2.PgStringArray `gorm:"column:external_id_list;default:'{};type:varchar(255);" `
	DependsOn       driver2.PgStringArray `gorm:"column:depends_on;type:text[];default:'{}'"`
	Audience        driver2.PgStringArray `gorm:"column:audience;type:text[];default:'{}'"`
	AvailableGroups driver2.PgUUIDArray   `gorm:"column:available_groups;type:uuid[];default:'{}'"`
}
type SecurityGroups []SecurityGroup

func (SecurityGroup) TableName() string {
	return "security_group"
}

func (s SecurityGroup) GetScopes() []string {
	return s.Role.Scopes
}

func (s SecurityGroup) GetSiteID() *uuid.UUID {
	return s.SiteID
}

func (s SecurityGroup) IsValid() bool {
	return s.ID != uuid.Nil && s.RoleID != uuid.Nil
}

func (s SecurityGroup) Validate() error {
	ee := map[string]string{}
	if s.Name == "" {
		ee["Name"] = "Name is required. "
	}
	if s.RoleID == uuid.Nil {
		ee["RoleID"] = "RoleID is required. "
	}
	//if s.SiteID == nil {
	//	ee["SiteID"] = "SiteID is required. "
	//}
	if s.Type == EXTERNAL && len(s.ExternalIdList) == 0 {
		ee["ExternalIdList"] = "ExternalIdList is required. "
	}
	if s.Type == MANUAL && len(s.DependsOn) == 0 {
		ee["DependsOn"] = "DependsOn is required. "
	}
	if s.Type != EXTERNAL && s.Type != MANUAL {
		ee["Type"] = "Type is required. Valid values are 'external' and 'manual'. "
	}
	if len(s.Audience) > 0 && len(s.AvailableGroups) == 0 {
		ee["AvailableGroups"] = "AvailableGroups is required. If Audience is not empty, AvailableGroups must be not empty too. "
	}
	if len(s.Audience) == 0 && len(s.AvailableGroups) > 0 {
		ee["Audience"] = "Audience is required. If AvailableGroups is not empty, Audience must be not empty too. "
	}

	if len(ee) > 0 {
		return errx.ValidationErr{ErrorMessages: ee}
	}

	return nil
}

func (SecurityGroup) SearchQuery() string {
	return "immutable_concat_security_group_cols(security_group) ILIKE ?"
}
