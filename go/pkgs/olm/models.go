package olm

import (
	"contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/structure"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
)

type (
	List struct {
		ID uuid.UUID `gorm:"column:id;type:uuid; primary_key; not null; default:uuid_generate_v4();"`
		content.Base
		Name             string
		Description      string
		StructureID      uuid.UUID            `gorm:"column:structure_id;type:uuid"`
		ContentTypes     driver.PgStringArray `gorm:"type:text[]"` // required: page, news, event, fragment
		OverrideSections driver.PgStringArray `gorm:"type:text[]"`
		Template         string
		Items            datatypes.JSONSlice[Item]
		Active           bool
		Tags             driver.PgUUIDArray `gorm:"type:uuid[]"`
		Distributed      bool
		commonModels.Tracking

		Structure structure.Structure `gorm:"foreignkey:StructureID"`
	}

	Item struct {
		ContentID uuid.UUID
		Overrides datatypes.JSONMap
	}
)

func (List) TableName() string {
	return "lists"
}

func (List) GetScopeEntity() string {
	return "cm.list"
}

func (List) SearchQuery() string {
	return "((lists.id || ' ' || lists.structure_id || ' ' || lists.name || ' ' || coalesce(lists.description, ' '))) ILIKE ?"
}
