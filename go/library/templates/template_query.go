package templates

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type ITemplate interface {
	GetTemplates(db *gorm.DB, params TemplateParams) ([]commonModels.Content, error)
	SearchTemplates(rq *shared.AppContext, params TemplateSearchQuery) result.Result[pagx.Paginated[commonModels.Content]]
}

type (
	templateInt    struct{}
	TemplateParams struct {
		Type           TemplateType
		Sites          []uuid.UUID
		Classification []string
	}
	TemplateSearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		TemplateParams
	}
)

func (ti templateInt) GetTemplates(db *gorm.DB, params TemplateParams) ([]commonModels.Content, error) {
	var templates = make([]commonModels.Content, 0)
	query := processWhere(db, params)
	return templates, query.Find(&templates).Error
}

func (ti templateInt) SearchTemplates(rq *shared.AppContext, params TemplateSearchQuery) result.Result[pagx.Paginated[commonModels.Content]] {
	var pag pagx.Paginated[commonModels.Content]
	query := processWhere(rq.TenantDatabase(), params.TemplateParams)
	return result.Check(pag, pagination.Paginate(query, params.Query, &pag))
}

func processWhere(db *gorm.DB, params TemplateParams) *gorm.DB {
	query := utils.
		NewCustomClauseBuilder(db).
		UnwrappedWhere(" ARRAY[?]::uuid[] && content.sites", params.Sites).
		Where("type = ?", commonModels.Template).
		Where("active = true")

	if params.Type == DCTTemplates {
		query = query.Where("(content.structure IS NOT NULL AND content.structure != '\"\"' AND content.structure != 'null')")
	}
	if params.Type == FormlessTemplates {
		query = query.Where("(content.structure IS NULL OR content.structure = '\"\"' OR content.structure = 'null')")
	}
	if len(params.Classification) > 0 {
		//query = query.Where(" (content.settings->>'classification')::jsonb @> ?::jsonb  ", params.Classification)
		//query = query.Where("jsonb_exists_any((content.settings->>'classification')::jsonb, ARRAY[?]::text[])", params.Classification)
		query = utils.NewCustomClauseBuilder(query).UnwrappedWhere(
			"jsonb_exists_any((content.settings->>'classification')::jsonb, ARRAY[?]::text[])",
			params.Classification)
	}
	return query
}

func ITemplateAdapter() ITemplate {
	return templateInt{}
}
