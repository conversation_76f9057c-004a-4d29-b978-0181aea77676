package utils

import (
	uuid "github.com/satori/go.uuid"
	"testing"
	"time"
)

func Test_Unmarshal(t *testing.T) {
	m := map[string]interface{}{
		"string":   "Hello World",
		"uuid":     "e4bf5663-4bdf-40fe-a4da-c6de2a0132ff",
		"time":     "2024-01-31T17:04:42.000Z",
		"timeNull": nil,
	}

	if v, ok := GetValueFromJSONMap[*time.Time](m, "timeNull"); !ok || v != nil {
		t.<PERSON><PERSON><PERSON>("Expected timeNull to be nil, got '%s'", v)
	}
	if v, ok := GetValueFromJSONMap[string](m, "string"); !ok || v != m["string"] {
		t.<PERSON><PERSON><PERSON>("Expected string to be 'Hello World', got '%s'", v)
	}
	if v, ok := GetValueFromJSONMap[uuid.UUID](m, "uuid"); !ok || v.String() != m["uuid"] {
		t.<PERSON><PERSON>rf("Expected uuid to be '%s', got '%s'", m["uuid"], v)
	}
	if v, ok := GetValueFromJSONMap[time.Time](m, "time"); !ok || v.Format("2006-01-02T15:04:05.000Z") != m["time"] {
		t.Errorf("Expected time to be '%s', got '%s'", m["time"], v)
	}
}

func Test_GetValueByPath(t *testing.T) {
	m := map[string]interface{}{
		"string": "Hello World",
		"object": map[string]interface{}{
			"string": "Nested Hello World",
		},
	}

	if v, ok := GetValueByPath[string](m, "string"); !ok || v != m["string"] {
		t.Errorf("Expected string to be 'Hello World', got '%s'", v)
	}
	if v, ok := GetValueByPath[string](m, "object.string"); !ok || v != m["object"].(map[string]interface{})["string"] {
		t.Errorf("Expected object.string to be 'Nested Hello World', got '%s'", v)
	}
}
