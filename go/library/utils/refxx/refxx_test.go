package refxx

import (
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"
)

func Test_PropertiesAccessor(t *testing.T) {
	data := struct {
		FromPath struct {
			ID     uuid.UUID
			Array  []string
			Deeper struct {
				Name string
			}
		}
	}{
		FromPath: struct {
			ID     uuid.UUID
			Array  []string
			Deeper struct {
				Name string
			}
		}{
			ID:    uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7"),
			Array: []string{"test1", "test2"},
			Deeper: struct {
				Name string
			}{
				Name: "test",
			},
		},
	}

	if _, err := ValueByPath[uuid.UUID](data, "FromPath.RandomName"); err == nil {
		t.Errorf("Expected error, got nothing")
	}

	if name, err := ValueByPath[string](data, "FromPath.Deeper.Name"); err != nil {
		t.<PERSON>rrorf("Expected no error, got %v", err)
	} else {
		assert.Equal(t, name, "test")
	}

	if id, err := ValueByPath[uuid.UUID](data, "FromPath.ID"); err != nil {
		t.Errorf("Expected no error, got %v", err)
	} else {
		assert.Equal(t, id, uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7"))
	}

	if arr, err := ValueByPath[[]string](data, "FromPath.Array"); err != nil {
		t.Errorf("Expected no error, got %v", err)
	} else {
		assert.Equal(t, arr, []string{"test1", "test2"})
	}
}

func Test_PropertiesAccessorInterface(t *testing.T) {
	data := struct {
		FromPath struct {
			ID    uuid.UUID
			Name  string
			Array []string
		}
	}{
		FromPath: struct {
			ID    uuid.UUID
			Name  string
			Array []string
		}{
			ID:    uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7"),
			Name:  "test",
			Array: []string{"test1", "test2"},
		},
	}

	if _, err := InterfaceByPath(data, "FromPath.RandomName"); err == nil {
		t.Errorf("Expected error for non-existent field")
	}

	id, err := InterfaceByPath(data, "FromPath.ID")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	} else if id != uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7") {
		t.Errorf("Expected ID to be cf7c279b-d6a8-4d73-a192-d37d019087e7, got %v", id)
	}

	arr, err := InterfaceByPath(data, "FromPath.Array")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	} else if !reflect.DeepEqual(arr, []string{"test1", "test2"}) {
		t.Errorf("Expected Array to be [test1 test2], got %v", arr)
	}
}

func Test_SetPrivateFieldByName(t *testing.T) {
	val := shared.AppContext{}

	sites := []tenancyModels.Site{{}, {}}
	if err := SetPrivateFieldByName(&val, "sites", sites); err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	valSites, _ := val.Sites()
	assert.Equal(t, len(valSites), len(sites))
}
