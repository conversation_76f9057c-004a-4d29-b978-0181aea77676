package commonControllers

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/claims"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login/google"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"net/http"
)

type (
	GoogleDirectoryLogin struct{}
)

// TODO: Confirm this should be removed since it's now been replaced by SocialLogin>Google
func (gdl GoogleDirectoryLogin) Authorize(w http.ResponseWriter, r *shared.AppContext, manager token.TokenManager[identity.PublicAccount]) {
	email, err := google.GetEmailFromResponseForm(*r.Request())
	if err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("Error decoding Google response")
		utils.WriteStatusJSON(w, http.StatusBadRequest, map[string]interface{}{"Error": "Error decoding Google response", "ErrorMessage": err.Error(), "ErrorData": err})
	}

	// This will likely error out since r.Request.Host is not base64 encoded
	appConfig := config.GetAppConfig()
	jwt, err := google.Authenticate(r, appConfig.GCloudIdentityCertificate, email, manager)
	if err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusUnauthorized)
		return
	}

	claims.SetCookies(w, jwt, r.Request().Host)
	utils.ResponseJson(w, utils.Message("success"), http.StatusOK)

	//cookie := &http.Cookie{
	//	Name:     appConfig.CookieName,
	//	Value:    token,
	//	Domain:   r.Request.Host,
	//	Path:     "/",
	//	Secure:   true,
	//	SameSite: http.SameSiteNoneMode,
	//	Expires:  time.Now().Add(conf.SessionCookieExpiry),
	//}
	//http.SetCookie(w, cookie)
	//if cacheCookie, err := login.GetPrivacyLevelCookie(appConfig.CookieName, r.Request.Host, account.PrivacyLevel, r.TenantID(), r.CurrentSiteID()); err == nil && cacheCookie != nil {
	//	http.SetCookie(w, cacheCookie)
	//}
	utils.ResponseJson(w, utils.Message("success"), http.StatusOK)

}
