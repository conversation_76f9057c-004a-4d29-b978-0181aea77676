package adminControllers

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/models"
	commonServices "contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/permissions/evaluators"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"strconv"
)

type (
	BusRouteController struct{}
)

func (brc BusRouteController) GetAdminBusRoute(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication

	tenantDB := r.TenantDatabase()
	useSiteIdString := r.Request().Form["useSiteId"]
	var useSiteId bool
	if len(useSiteIdString) > 0 {
		useSiteId, _ = strconv.ParseBool(useSiteIdString[0])
	} else {
		useSiteId = true
	}

	routes, err := commonServices.GetBusRouteResults(tenantDB, r.CurrentSiteIDNullable(), useSiteId)
	if err != nil {
		http.NotFound(w, r.Request())
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	json.NewEncoder(w).Encode(routes)
}

func (brc BusRouteController) PostAdminBusRoute(w http.ResponseWriter, r *shared.AppContext) {
	var route commonModels.BusRoute
	if err := evaluators.ForCreate(r, &route); err != nil {
		utils.WriteResponseJSON(w, nil, err)
	}
	logger := logging.FromRequest(r.Request())

	if !route.Validate() {
		utils.WriteResponseJSON(w, nil, errors.New(fmt.Sprintf("invalid object: %v", route)))
		return
	}

	BusRoute, err := adminServices.CreateBusRoute(r.TenantDatabase(), route)
	if err != nil {
		logger.Err(err).Msg("failed to create bus route")
		utils.ResponseJson(w, utils.Message("not able to create requested content"), http.StatusBadRequest)
		return
	}
	utils.WriteResponseJSON(w, BusRoute, nil)
}

func (brc BusRouteController) DeleteAdminBusRoute(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := uuid.FromString(p["id"])
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var route commonModels.BusRoute
	if err := evaluators.ForActionByID(r, &route, id, "delete"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if err := r.TenantDatabase().Model(&commonModels.BusRoute{}).Where("id = ?", id).Update("active", false).Error; err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("DB Error deleting bus route")
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResponseJSON(w, nil, nil)
}

func (brc BusRouteController) UpdateAdminBusRoute(w http.ResponseWriter, r *shared.AppContext) {
	var route commonModels.BusRoute
	if err := evaluators.ForUpdate(r, &route, &commonModels.BusRoute{}); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	logger := logging.FromRequest(r.Request())

	if !route.Validate() {
		utils.WriteResponseJSON(w, nil, errors.New(fmt.Sprintf("invalid object: %v", route)))
		return
	}

	BusRoute, err := adminServices.UpdateBusRoute(r.TenantDatabase(), route)
	if err != nil {
		logger.Err(err).Str("routeId", BusRoute.ID.String()).Msg("failed to edit bus route")
		utils.ResponseJson(w, utils.Message("not able to edit requested content"), http.StatusBadRequest)
	}
	utils.WriteResponseJSON(w, BusRoute, nil)
}
