package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
)

func init() {
	handlebars.RegisterHelper("renderContactForm", renderContactForm)
}

func renderContactForm() string {
	var form string
	form =
		`
<form id='contact' class='contact-form' method="POST" action="/api/v1/contact">
	
	<div class="input-field">
	  <label for="name">Name:</label>
	  <input class='contact-form-text' type="text" id="name" name="name" required>
	</div>
	
	
	<div class="input-field">
	  <label for="phone">Phone Number:</label>
	  <input class='contact-form-text' type="tel" placeholder="e.g 6045555555" pattern="[0-9]{3}[0-9]{3}[0-9]{4}" id="phone" name="phone" required>
	</div>
	
	<div class="input-field">
	  <label for="email">Email:</label>
	  <input class='contact-form-text' type="email" id="email" name="email" required>
	</div>
	
	<div class="input-field">
	  <label for="message">Message:</label>
	  <textarea id='ta' class='contact-form-textarea' id="message" name="message"  required></textarea>
	</div>

    <div class="g-recaptcha" data-sitekey="6Lc1jV8aAAAAAIw2JS68qQYuzrIutL8Bhm3N8FC7"></div>
		
	<div class="input-buttons">
		<input class='contact-form-button' type="submit" value="Send">
	</div>
</form>
`
	return form
}
