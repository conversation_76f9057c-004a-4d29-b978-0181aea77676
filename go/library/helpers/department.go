package helpers

import (
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	uuid "github.com/satori/go.uuid"
)

func init() {
	handlebars.RegisterHelper("isDepartment", isDepartment)
}

// #equal helper
func isDepartment(a interface{}, options *handlebars.Options) interface{} {
	switch v := a.(type) {
	case publicModels.HandlebarsPrimaryNavigation:
		if v.DepartmentId.UUID != uuid.Nil && v.DepartmentId.Valid {
			return options.Fn()
		}
	case publicModels.ContentForHandlebars:
		if v.DepartmentId.UUID != uuid.Nil && v.DepartmentId.Valid {
			return options.Fn()
		}
	default:
		return options.Inverse()
	}
	return options.Inverse()
	//return options.Inverse()
}
