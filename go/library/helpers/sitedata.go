package helpers

import (
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/public/utils/handlebars"
)

func init() {
	handlebars.RegisterHelper("siteData", siteData)
	handlebars.RegisterHelper("siteHasInstagram", siteHasInstagram)
}

func siteHasInstagram(value tenancyModels.SiteViewModel, options *handlebars.Options) string {
	// Deprecated (2025-01-07) No longer supporting Instagram.
	// Code commented out and kept for reference.
	//if extendedToken, err := value.Site.GetInstagramInfo(); err == nil {
	//	if extendedToken.LastError == nil || len(*extendedToken.LastError) == 0 {
	//		return options.Fn()
	//	}
	//}
	return options.Inverse()
}

func siteData(value tenancyModels.SiteViewModel, path string, options *handlebars.Options) string {
	return renderJsonbFieldPath(value.Settings, path, options)
}
