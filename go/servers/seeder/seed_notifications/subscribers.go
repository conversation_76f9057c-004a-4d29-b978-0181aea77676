package seed_notifications

import (
	"contentmanager/library/utils/converters"
	"contentmanager/logging"
	"contentmanager/pkgs/notifications/models"
	"contentmanager/servers/seeder/fakex"
	"context"
	"github.com/brianvoe/gofakeit/v6"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

func SeedSubscribers(ctx context.Context, db *gorm.DB) {
	for i := 0; i < 10; i++ {
		ss := make([]models.Subscriber, 0, 100)
		for j := 0; j < 100; j++ {
			id := uuid.NewV4()
			s := models.Subscriber{
				FirstName:               gofakeit.FirstName(),
				LastName:                gofakeit.LastName(),
				ManageCode:              &id,
				Email:                   fakex.EmailAmazon(),
				EmailConfirmedAt:        converters.AsPointer(gofakeit.DateRange(time.Now().Add(-12*30*24*time.Hour), time.Now())),
				EmailConfirmationSentAt: converters.AsPointer(gofakeit.DateRange(time.Now().Add(-14*30*24*time.Hour), time.Now().Add(-13*30*24*time.Hour))),
				Phone:                   nil,
				PhoneConfirmedAt:        nil,
				PhoneConfirmationSentAt: nil,
				PhoneConfirmationCode:   nil,
			}
			ss = append(ss, s)
		}
		if err := db.Create(&ss).Error; err != nil {
			logging.FromContext(ctx).Error().Err(err).Msg("Can't save subscribers")
		}
	}
}
