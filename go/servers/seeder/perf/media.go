package perf

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
)

func StartDownloads(urls map[string]struct{}, parallelism int) {
	var wg sync.WaitGroup

	// Create a channel to control the number of concurrent goroutines
	semaphore := make(chan struct{}, parallelism)

	for url, _ := range urls {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire a slot
		go func(u string) {
			defer func() { <-semaphore }() // Release the slot
			downloadURL(u, &wg)
		}(url)
	}

	wg.Wait()
}

// downloadURL emulates downloading from a URL by reading the response body without storing it.
func downloadURL(url string, wg *sync.WaitGroup) {
	defer wg.Done()

	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			host, port, err := net.SplitHostPort(addr)
			if err != nil {
				return nil, err
			}

			// Check if the domain is a .localhost domain
			if strings.HasSuffix(host, ".localhost") {
				addr = net.JoinHostPort("127.0.0.1", port)
			}

			// Use a custom dialer for establishing the connection
			dialer := &net.Dialer{}
			return dialer.DialContext(ctx, network, addr)
		},
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // Ignore SSL errors, analogous to SslOptions in the .NET example
		},
	}
	// Create a custom HTTP client that ignores SSL errors
	client := &http.Client{
		Transport: transport,
	}

	// Start an infinite loop to emulate indefinite downloading
	for {
		resp, err := client.Get(url)
		if err != nil {
			fmt.Printf("Error fetching %s: %s\n", url, err)
			continue
		}

		// Read the response body without storing it
		_, err = io.Copy(io.Discard, resp.Body)
		if err != nil {
			fmt.Printf("Error reading response body from %s: %s\n", url, err)
		}
		resp.Body.Close()

		fmt.Printf("Successfully emulated download from %s\n", url)
	}
}
