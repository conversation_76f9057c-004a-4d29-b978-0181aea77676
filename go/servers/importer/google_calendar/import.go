package google_calendar

import (
	"contentmanager/library/utils/slicexx"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/content"
	GI "contentmanager/pkgs/googleapi"
	"contentmanager/pkgs/settings"
	"contentmanager/servers/importer/common"
	"contentmanager/servers/importer/common/integration"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/api/calendar/v3"
	"gorm.io/gorm"
	"time"
)

var (
	ImportSource = "GoogleCalendar"
	// This is arbitrarily set and could be revised / have tenant based configuration
	defaultMinAge = 1
)

func GCalendarConsistentId(eventId string) uuid.UUID {
	return importers.GenerateConsistentID("google-calendar-" + eventId)
}

func Import(importer *integration.ImportContext) {
	importer.WithSubject(ImportSource)

	AdminUser, err := getGoogleCalendarSecrets(importer.DB)
	if err != nil {
		// Early out for Districts that don't utilize the Google Calendar integration.
		// Silently return if ErrRecordNotFound
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			importer.LogAndRecordErr("failed to retrieve google calendar secrets", err)
		}
		return
	}
	serviceConfig := config.GetAppConfig()
	if serviceConfig == nil || len(serviceConfig.GCloudIdentityCertificate) == 0 {
		importer.Logger.Error().Msg("invalid identity certificate provided")
		return
	}

	var result []settings.Settings
	if err := importer.DB.
		Where("type = ?", settings.GoogleCalendar).
		Where("active").
		Find(&result).Error; err != nil {
		importer.Logger.Err(err).Msg("failed to retrieve settings")
		return
	}
	// Age - Content that is added to ContentList but not found in import is set active = false
	// Age used for API query (minAge) should be same date as given to GetContentList
	Age := 5
	FiveYearsAgo := time.Now().AddDate(-(Age), 0, 0)
	ContentList, err := importers.GetContentList(importer.DB, ImportSource, &FiveYearsAgo)
	if err != nil {
		importer.Logger.Err(err).Msg("failed to retrieve Content list")
		return
	}
	for _, setting := range result {
		l := importer.Logger.With().Str("Settings.ID", setting.ID.String()).Logger()

		calendarId, err := getCalendarIdFromSettings(setting.Data)
		if err != nil {
			l.Err(err).Msg("failed to get calendarId from Settings")
			continue
		}
		l = l.With().Str("calendarId", calendarId).Logger()

		calendarService, err := GI.NewCalendar(serviceConfig.GCloudIdentityCertificate, AdminUser, calendarId)
		if err != nil {
			statusCode, body := GI.UnwrapError(err)
			switch statusCode {
			case 404:
				importer.LogAndRecordInfo(fmt.Sprintf("Calendar Not Found: [%s] - Please check if it exists", calendarId), body)
			case 403:
				importer.LogAndRecordInfo(fmt.Sprintf("Access Denied for resource: [%s]", calendarId), body)
			default:
				l.Err(err).Str("body", body).Msg("failed to create calendar service")
			}
			continue
		}
		calendarService.WithMinAge(Age)
		for calendarService.CanPaginate() {
			events, err := calendarService.GetEvents()
			if err != nil {
				l.Err(err).Msg("failed to retrieve events with calendarService")
				break
			}
			for _, event := range events {
				if event == nil {
					continue
				}
				// The Event has been deleted in Google Calendar
				if event.Status == "cancelled" {
					// Content within ContentList is already set to Active = false, so we're safe
					// to early out even if the Content was previously imported into Content Manager
					continue
				}
				ID := GCalendarConsistentId(event.Id)
				Previous, OK := ContentList[ID]
				if OK {
					// The event has not been modified since previously imported
					if !requiresUpdate(Previous, event, setting) {
						delete(ContentList, ID)
						continue
					}
					Previous.Title = event.Summary
					Previous.Content = event.Description
					Previous.Updated = parseTime(event.Updated)
					Previous.Settings = getImportedContentSettings(calendarId, *event)
					Previous.Sites = setting.Sites
					Previous.Active = true
					Previous.Workspace = "live"
					Previous.EffectiveIn = []string{"sandbox"}
					ContentList[ID] = Previous
				} else {
					// Don't create records that have no available title
					if len(event.Summary) == 0 && len(event.Description) == 0 {
						continue
					}
					Content := TranslateEventToContent(*event, calendarId, setting.Sites)
					ContentList[ID] = Content
				}
			}
		}
	}
	// Process all Settings configs for Google Calendar and save
	// Since Un-updated Content in the map is Active = False, we don't want to save within the loop per Settings
	if len(ContentList) == 0 {
		return
	}
	Contents := ContentList.ToBatchesOf(100)
	for _, chunks := range Contents {
		if err := importer.DB.Save(&chunks).Error; err != nil {
			importer.Logger.Err(err).Msg("error saving imported content")
			continue
		}
	}
}
func requiresUpdate(content content.Content, calendarEvent *calendar.Event, settings settings.Settings) bool {
	if !slicexx.EqualComparable(content.Sites, settings.Sites) {
		return true
	}
	var updatedAt time.Time
	if err := jsonxx.UnmarshalJSONAtPath(content.Settings, "importInfo.updated_time", &updatedAt); err != nil {
		return true
	}
	var location string
	if err := jsonxx.UnmarshalJSONAtPath(content.Settings, "location.displayName", &location); err != nil {
		return true
	}
	return !parseTime(calendarEvent.Updated).Equal(updatedAt) || calendarEvent.Location != location || calendarEvent.Summary != content.Title
}

func getGoogleCalendarSecrets(db *gorm.DB) (string, error) {
	var temp = struct {
		AdminUser string `json:"adminUser"`
	}{}
	return temp.AdminUser, integration.GetSecrets(db, settings.GoogleCalendarSecrets, &temp)
}

func getCalendarIdFromSettings(data []byte) (string, error) {
	tmp := struct {
		GoogleCalendarID string `json:"GoogleCalendarID"`
	}{}
	err := json.Unmarshal(data, &tmp)
	return tmp.GoogleCalendarID, err
}
