package facebook_api

import (
	"encoding/json"
	"time"
)

const FacebookTimeFormat = "2006-01-02T15:04:05+0000"

type (
	Post struct {
		ID         string `json:"id"`
		Message    string `json:"message"`
		PictureURL string `json:"full_picture"`
		Route      string `json:"permalink_url"`
		Privacy    struct {
			Value string `json:"value"`
		}
		IsPublished bool   `json:"is_published"`
		IsHidden    bool   `json:"is_hidden"`
		IsExpired   bool   `json:"is_expired"`
		UpdatedTime string `json:"updated_time"`
		CreatedTime string `json:"created_time"`
	}
	PostsWithPager WithPagination[Post]
)

func (p Post) IsActive() bool {
	return !p.IsExpired && !p.IsHidden && p.IsPublished
}
func (p Post) PrivacyLevel() int {
	if p.Privacy.Value != "EVERYONE" {
		return 2
	}
	return 0
}
func (p Post) Created() time.Time {
	created, err := time.Parse(FacebookTimeFormat, p.CreatedTime)
	if err != nil {
		return time.Now()
	}
	return created
}
func (p Post) LastModified() time.Time {
	updated, err := time.Parse(FacebookTimeFormat, p.UpdatedTime)
	if err != nil {
		return time.Now()
	}
	if updated.After(p.Created()) {
		return updated
	}
	return p.Created()
}
func (p Post) IsEqual(p2 PostState) bool {
	// This shouldn't be possible, but lacking confirmation.
	if p2.PostID != p.ID {
		return false
	}
	// The post has not been updated since it was imported
	if p2.UpdatedTime == p.UpdatedTime {
		return true
	}
	// Message and Privacy are the only two fields that can change
	if p2.Message == p.Message && p2.Privacy == p.Privacy.Value {
		return true
	}
	return false
}
func GetPostState(bytes []byte) PostState {
	var temp = struct {
		State PostState `json:"importInfo"`
	}{}
	_ = json.Unmarshal(bytes, &temp)
	return temp.State
}

type PostState struct {
	UpdatedTime string `json:"updated_time"`
	PostID      string `json:"id"`
	Message     string `json:"message"`
	Privacy     string `json:"privacy"`
}
