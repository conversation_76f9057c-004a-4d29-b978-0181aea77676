package outlook

import (
	"contentmanager/pkgs/msgraph"
	"contentmanager/pkgs/settings"
	importers "contentmanager/servers/importer/common"
	"contentmanager/servers/importer/common/integration"
	"contentmanager/servers/importer/common/msgraphxx"
	"errors"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

const (
	ImportSource = "outlook"
)

func Import(importer *integration.ImportContext) {
	defer func() {
		if r := recover(); r != nil {
			importer.Logger.Error().Interface("recover", r).Msg("Panic")
		}
	}()
	importer.WithSubject(ImportSource)
	client, err := NewAPIClient(importer.DB)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if errors.Is(err, importers.ErrInvalidSecrets) {
			importer.LogAndRecordInfo("malformed or invalid client secrets")
		} else {
			importer.Logger.Err(err).Msg("failed to create client")
		}
		return
	}

	configurations, err := importer.GetConfigurations(settings.Outlook)
	if err != nil {
		importer.Logger.Err(err).Msg("Failed to obtain outlook config for tenant")
		return
	}
	if len(configurations) == 0 {
		return
	}
	//FiveYearsAgo := time.Now().AddDate(-5, 0, 0)
	ContentList, err := importers.GetContentList(importer.DB, ImportSource, nil)
	if err != nil {
		importer.Logger.Err(err).Msg("failed to retrieve existing Content list")
		return
	}
	// Interface might not be necessary, as we can possibly just modify the function signature to be able to
	// test things like TranslateEvents by passing in Events, like below.
	// Interface would still be useful for testing things like 'getMailbox' vs 'getCalendar'
	resourcer := msgraphxx.NewResourcer(client)
	for _, config := range configurations {
		events, err := getEvents(resourcer, ContentList, config.Id)
		if err != nil {
			importer.Logger.Err(err).Str("externalId", config.Id).Msg("Failed to obtain events")
		}
		if err := TranslateEvents(importer, ContentList, config, events); err != nil {
			importer.Logger.Err(err).
				Str("settingsId", config.SettingsID.String()).
				Str("externalId", config.Id).
				Msg("error translating events")
		}
	}

	if len(ContentList) == 0 {
		return
	}
	for _, contents := range ContentList.ToBatchesOf(50) {
		if err := importer.DB.Save(&contents).Error; err != nil {
			importer.Logger.Err(err).Msg("error saving content")
			return
		}
	}
}
func getEvents(resourcer CalendarResourcer, ContentList importers.ContentList, configId string) (msgraph.CalendarEvents, error) {
	events, err := getCalendarEvents(resourcer, configId)
	if err != nil {
		// Hotfix 2023-09-11: When a calendar request times out, or any other error - all events from that import are deleted.
		// We can either getContentMap for each integration (by settings.calendarId) or this potentially temporary fix of just deleting from the map here.
		for _, content := range ContentList {
			externalId := gjson.Get(string(content.Settings), "importInfo.externalId")
			if externalId.String() == configId {
				delete(ContentList, content.ID)
			}
			//var importInfo = content.GetSettings().ImportInfo
			//if importInfo.Calendar == configId {
			//	delete(ContentList, content.ID)
			//}
		}
	}
	return events, err
}
