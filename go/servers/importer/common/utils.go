package importers

import (
	"bytes"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"encoding/json"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
)

var (
	ErrInvalidSecrets = errors.New("invalid or malformed secrets")
)

func GenerateConsistentID(source string) uuid.UUID {
	return uuid.FromStringOrNil(utils.GenerateUUIDFromString(source).String())
}

// ClearImportHistory
// Experimented with implementation of Diff checking in Facebook & Google Calendar, but this is an acceptable approach.
// FB & GC Don't have modifiable content, so it doesn't need any history at all.
// Outlook & Edsby can be modified, ClearImportHistory only deletes content that has not been updated by a User
func ClearImportHistory(DB *gorm.DB) error {
	return DB.Exec("" +
		"delete from content_history" +
		" where (settings->'importInfo'->>'source') is not null and (settings->'importInfo'->>'source') in ('facebook', 'outlook', 'edsby', 'GoogleCalendar') " +
		" and (settings->>'imported') = 'true' " +
		" and publisher = '45f06f48-a93c-414e-b9a0-7582e0abc085' " +
		"").Error
}

func MapTagNameToID(tagReferences map[string]uuid.UUID, categories []string) []uuid.UUID {
	var tagIds = []uuid.UUID{}
	for _, category := range categories {
		if tagId, ok := tagReferences[strings.ToLower(category)]; ok {
			tagIds = append(tagIds, tagId)
		}
	}
	return tagIds
}

func TranslateHtmlToDefaultStructuredContent(content string) (json.RawMessage, error) {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(map[string]interface{}{
		"mainContent": map[string]interface{}{
			"lexical": map[string]interface{}{
				"html":   content,
				"json":   map[string]interface{}{},
				"engine": "ckeditor",
			},
		},
	})
	return buffer.Bytes(), err
}

func MergeTags(t1, t2 []commonModels.Tag) []commonModels.Tag {
	var m = make(map[uuid.UUID]commonModels.Tag, 0)
	for _, v := range append(t1, t2...) {
		m[v.ID] = v
	}
	var r = make([]commonModels.Tag, 0)
	for _, v := range m {
		r = append(r, v)
	}
	return r
}
