package lib

import (
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/pkgs/service_context"
	"contentmanager/pkgs/third_party_content"
	"gorm.io/gorm"
)

func LookupCustomContentForTenant(sc service_context.ServiceContext, tenant multitenancy.Tenant) {
	l := sc.Logger().With().
		Str("tenant", tenant.Name).
		Logger()

	var tenantSites []multitenancy.Site
	if err := sc.MultitenancyAccessor().TenancyDB().
		Where("tenant_id = ?", tenant.ID).
		Where("active").
		Find(&tenantSites).Error; err != nil {
		l.Err(err).Msg("unable to get sites for tenant")
		return
	}
	db := sc.TenantDB(tenant.ID)
	if db == nil {
		l.Error().Msg("failed to establish database connections")
		return
	}
	ci, err := third_party_content.FromJson(tenant.Settings)
	if err != nil {
		return
	}
	sites := slicexx.Filter(tenantSites, func(site multitenancy.Site) bool {
		return ci.CustomIntegrations.AffectsSite(site.ID, site.Type)
	})
	for _, site := range sites {
		if err := ImportCustomThirdPartyContent(db, site, ci); err != nil {
			l.Err(err).Msg("failed to import custom third party content")
		}
	}
	if err = DeleteExpiredThirdPartyContent(db); err != nil {
		l.Err(err).Msg("error deleting expired third party content")
	}
	return
}

func DeleteExpiredThirdPartyContent(dbCon *gorm.DB) error {
	return dbCon.Exec(" DELETE FROM third_party_content WHERE last_modified < NOW() - INTERVAL '8 HOUR' ").Error
}
