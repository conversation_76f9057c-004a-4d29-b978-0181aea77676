package middlewares

import (
	"contentmanager/library/httpService"
	"net/http"
	"os"
)

func AppVersion() httpService.Handler {
	v := os.Getenv("APP_VERSION")
	if len(v) == 0 {
		v = "dev"
	}
	a := os.Getenv("APP_NAME")
	if len(a) == 0 {
		a = "ie-app"
	}
	v = a + ":" + v

	return func(res http.ResponseWriter, req *http.Request, c httpService.Context) {
		res.Header().Set("X-IE-App-Version", v)
		c.Next()
	}
}
