package middlewares

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/logging/stacktrace"
	"contentmanager/pkgs/auth/login"
	"errors"
	"fmt"
	"log"
	"net/http"
)

// Recovery returns a middleware that recovers from any panics and writes a 500 if there was one.
// While Middleware is in development mode, Recovery will also output the panic as HTML.
func Recovery() httpService.Handler {
	return func(r *shared.AppContext, c httpService.Context, res http.ResponseWriter) {
		defer func() {
			if err := recover(); err != nil {
				l := r.Logger().Error()
				castErr, okErr := err.(error)
				if okErr {
					l = l.Stack().Err(castErr)
				} else if castErrStr, okErrStr := err.(string); okErrStr {
					castErr = errors.New(castErrStr)
					l = l.Stack().Err(castErr)
				} else {
					l = l.Interface("error", err).Interface("stack", stacktrace.Stack(3))
				}
				l.Msg("PANIC!")

				if logging.IsDebug() {
					log.Println(logging.ColorRed,
						fmt.Sprintf("\n%s\n", stacktrace.StackForDebug(3)),
						logging.ColorReset)
				}

				// If it's a request from the admin application, return the error for debugging.
				if login.IsAdminApplicationURL(r.Request().Host) {
					utils.WriteResponseJSON(res, nil, castErr)
					return
				}

				res.WriteHeader(500)
				if errorPage := GetErrorPage(r); errorPage != "" {
					res.Write([]byte(errorPage))
					return
				}
				res.Write([]byte("500: Internal Server Error"))
			}
		}()

		c.Next()
	}
}
